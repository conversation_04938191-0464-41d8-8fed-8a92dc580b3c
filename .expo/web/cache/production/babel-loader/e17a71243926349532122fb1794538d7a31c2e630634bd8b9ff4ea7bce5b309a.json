{"ast": null, "code": "if(typeof global.setImmediate!=='function'){console.log('Adding setImmediate polyfill');global.setImmediate=function(callback,...args){return setTimeout(()=>callback(...args),0);};}if(typeof global.clearImmediate!=='function'){console.log('Adding clearImmediate polyfill');global.clearImmediate=function(id){return clearTimeout(id);};}console.log('Timer polyfills loaded');", "map": {"version": 3, "names": ["global", "setImmediate", "console", "log", "callback", "args", "setTimeout", "clearImmediate", "id", "clearTimeout"], "sources": ["/Users/<USER>/Desktop/Shake2/timer-polyfills.js"], "sourcesContent": ["// timer-polyfills.js - Add missing timer functions\n// This adds polyfills for setImmediate and clearImmediate which are missing\n\nif (typeof global.setImmediate !== 'function') {\n    console.log('Adding setImmediate polyfill');\n    global.setImmediate = function(callback, ...args) {\n      return setTimeout(() => callback(...args), 0);\n    };\n  }\n  \n  if (typeof global.clearImmediate !== 'function') {\n    console.log('Adding clearImmediate polyfill');\n    global.clearImmediate = function(id) {\n      return clearTimeout(id);\n    };\n  }\n  \n  console.log('Timer polyfills loaded');"], "mappings": "AAGA,GAAI,MAAO,CAAAA,MAAM,CAACC,YAAY,GAAK,UAAU,CAAE,CAC3CC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3CH,MAAM,CAACC,YAAY,CAAG,SAASG,QAAQ,CAAE,GAAGC,IAAI,CAAE,CAChD,MAAO,CAAAC,UAAU,CAAC,IAAMF,QAAQ,CAAC,GAAGC,IAAI,CAAC,CAAE,CAAC,CAAC,CAC/C,CAAC,CACH,CAEA,GAAI,MAAO,CAAAL,MAAM,CAACO,cAAc,GAAK,UAAU,CAAE,CAC/CL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7CH,MAAM,CAACO,cAAc,CAAG,SAASC,EAAE,CAAE,CACnC,MAAO,CAAAC,YAAY,CAACD,EAAE,CAAC,CACzB,CAAC,CACH,CAEAN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC", "ignoreList": []}, "metadata": {"hasCjsExports": false}, "sourceType": "script", "externalDependencies": []}