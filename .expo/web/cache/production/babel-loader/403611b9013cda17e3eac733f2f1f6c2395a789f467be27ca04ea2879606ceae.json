{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{NavigationContainer}from'@react-navigation/native';import{createStackNavigator}from'@react-navigation/stack';import{Accelerometer}from'expo-sensors';import*as Location from'expo-location';import{io}from'socket.io-client';import AsyncStorage from'@react-native-async-storage/async-storage';import StatusBar from\"react-native-web/dist/exports/StatusBar\";import View from\"react-native-web/dist/exports/View\";import Alert from\"react-native-web/dist/exports/Alert\";import Linking from\"react-native-web/dist/exports/Linking\";import Platform from\"react-native-web/dist/exports/Platform\";import'react-native-gesture-handler';import LoginScreen from'./screens/LoginScreen';import RegisterScreen from'./screens/RegisterScreen';import HomeScreen from'./screens/HomeScreen';import SettingsScreen from'./screens/SettingsScreen';import ChatScreen from'./screens/ChatScreen';import MatchScreen from'./screens/MatchScreen';import ViewProfileScreen from'./screens/ViewProfileScreen';import ProfileScreen from'./screens/ProfileScreen';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Stack=createStackNavigator();export default function App(){const[isLoggedIn,setIsLoggedIn]=useState(false);const[user,setUser]=useState(null);const[shakeDetected,setShakeDetected]=useState(false);const[location,setLocation]=useState(null);const[maxDistance,setMaxDistance]=useState(5);const[matches,setMatches]=useState([]);const[blockedUsers,setBlockedUsers]=useState([]);const socketRef=useRef(null);const[serverAddress,setServerAddress]=useState('');const navigationRef=useRef(null);const inactivityCheckIntervalRef=useRef(null);const[userProfile,setUserProfile]=useState(null);const[minAge,setMinAge]=useState(18);const[maxAge,setMaxAge]=useState(100);useEffect(()=>{const checkLoginStatus=async()=>{try{const userJSON=await AsyncStorage.getItem('user');const storedServer=await AsyncStorage.getItem('serverAddress');const storedMaxDistance=await AsyncStorage.getItem('maxDistance');const storedMinAge=await AsyncStorage.getItem('minAge');const storedMaxAge=await AsyncStorage.getItem('maxAge');let loginSuccessful=false;if(userJSON&&storedServer){console.log('Found stored user data:',userJSON);try{const userData=JSON.parse(userJSON);if(!userData.id){console.error('Stored user data missing ID:',userData);await AsyncStorage.removeItem('user');}else{try{const response=await fetch(`http://${storedServer}/api/debug/user/${userData.id}`,{method:'GET',headers:{'Content-Type':'application/json'}});const data=await response.json();if(!response.ok||!data.exists){console.log('User no longer exists in database, clearing credentials');await AsyncStorage.removeItem('user');}else{console.log('User validation successful');console.log('Logging in with stored credentials:',userData.username,userData.id);setUser(userData);setIsLoggedIn(true);setServerAddress(storedServer);loginSuccessful=true;}}catch(validationError){console.error('Error validating user:',validationError);await AsyncStorage.removeItem('user');}}}catch(parseError){console.error('Error parsing stored user data:',parseError);await AsyncStorage.removeItem('user');}}else{console.log('No stored user data found or missing server address');}if(storedServer){setServerAddress(storedServer);}if(storedMaxDistance){setMaxDistance(parseFloat(storedMaxDistance)||5);}if(storedMinAge){setMinAge(parseInt(storedMinAge)||18);}if(storedMaxAge){setMaxAge(parseInt(storedMaxAge)||100);}if(!loginSuccessful){setIsLoggedIn(false);setUser(null);}}catch(error){console.error('Error checking login status:',error);setIsLoggedIn(false);setUser(null);}};checkLoginStatus();},[]);const updateAgeRange=async(min,max)=>{setMinAge(min);setMaxAge(max);try{await AsyncStorage.setItem('minAge',min.toString());await AsyncStorage.setItem('maxAge',max.toString());}catch(error){console.error('Error saving age range settings:',error);}};useEffect(()=>{const loadUserData=async()=>{try{if(user?.id){const storedMatches=await AsyncStorage.getItem(`matches_${user.id}`);if(storedMatches){setMatches(JSON.parse(storedMatches));}const storedBlockedUsers=await AsyncStorage.getItem(`blocked_${user.id}`);if(storedBlockedUsers){setBlockedUsers(JSON.parse(storedBlockedUsers));}const storedProfile=await AsyncStorage.getItem(`profile_${user.id}`);if(storedProfile){setUserProfile(JSON.parse(storedProfile));}}}catch(error){console.error('Error loading user data:',error);}};if(isLoggedIn&&user){loadUserData();}},[isLoggedIn,user]);useEffect(()=>{const checkProfileCompletion=async()=>{if(isLoggedIn&&user?.id){try{const storedProfile=await AsyncStorage.getItem(`profile_${user.id}`);let profileData=null;if(storedProfile){profileData=JSON.parse(storedProfile);}const isProfileComplete=profileData&&profileData.description&&profileData.age&&profileData.images&&profileData.images.length>=3;if(!isProfileComplete&&navigationRef.current){console.log('Profile incomplete, redirecting to profile setup');setTimeout(()=>{navigationRef.current.reset({index:0,routes:[{name:'ProfileScreen',params:{isInitialSetup:true}}]});},500);}}catch(error){console.error('Error checking profile completion:',error);}}};checkProfileCompletion();},[isLoggedIn,user]);const handleUpdateProfile=profileData=>{setUserProfile(profileData);};const isUserBlocked=userId=>{return blockedUsers.includes(userId);};const isUserInChatWith=userId=>{if(!navigationRef.current)return false;try{const currentRoute=navigationRef.current.getCurrentRoute();if(currentRoute?.name==='Chat'&&currentRoute?.params?.match?.userId===userId){return true;}}catch(e){console.error('Error checking current route:',e);}return false;};const handleBlockUser=async userId=>{try{if(!user?.id||!userId){console.error('Cannot block user: Missing user ID or target ID');return;}if(isUserBlocked(userId)){return;}const updatedBlockedUsers=[...blockedUsers,userId];setBlockedUsers(updatedBlockedUsers);await AsyncStorage.setItem(`blocked_${user.id}`,JSON.stringify(updatedBlockedUsers));await handleDeleteChat(userId,true);if(socketRef.current&&socketRef.current.connected){socketRef.current.emit('blockUser',{userId:user.id,blockedUserId:userId});}console.log('User blocked successfully');}catch(error){console.error('Error blocking user:',error);}};const handleUnblockUser=async userId=>{try{if(!user?.id||!userId){console.error('Cannot unblock user: Missing user ID or target ID');return;}const updatedBlockedUsers=blockedUsers.filter(id=>id!==userId);setBlockedUsers(updatedBlockedUsers);await AsyncStorage.setItem(`blocked_${user.id}`,JSON.stringify(updatedBlockedUsers));if(socketRef.current&&socketRef.current.connected){socketRef.current.emit('unblockUser',{userId:user.id,unblockedUserId:userId});}console.log('User unblocked successfully');}catch(error){console.error('Error unblocking user:',error);}};const saveMessageToStorage=async messageData=>{try{if(!messageData||!messageData.senderId||!messageData.receiverId){console.error('Invalid message data for storage');return;}const chatId=[messageData.senderId,messageData.receiverId].sort().join('_');const storedMessagesJSON=await AsyncStorage.getItem(`chat_${chatId}`);let storedMessages=[];if(storedMessagesJSON){storedMessages=JSON.parse(storedMessagesJSON);}const formattedMessage={id:messageData.id||Date.now().toString(),text:messageData.text,senderId:messageData.senderId,timestamp:messageData.timestamp||new Date().toISOString()};const messageExists=storedMessages.some(msg=>msg.id===formattedMessage.id);if(messageExists){return;}const updatedMessages=[...storedMessages,formattedMessage];await AsyncStorage.setItem(`chat_${chatId}`,JSON.stringify(updatedMessages));updateMatchLastActivity(messageData.senderId===user.id?messageData.receiverId:messageData.senderId);console.log('Message saved to storage successfully');}catch(error){console.error('Error saving message to storage:',error);}};const updateMatchLastActivity=matchUserId=>{setMatches(prevMatches=>{const updatedMatches=prevMatches.map(match=>{if(match.userId===matchUserId){return Object.assign({},match,{lastActivity:new Date().toISOString(),hasActivity:true});}return match;});try{if(user?.id){AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));}}catch(e){console.error('Error saving updated matches:',e);}return updatedMatches;});};const handleDeleteChat=async(matchUserId,isBlocking=false)=>{try{if(!user?.id||!matchUserId){console.error('Cannot delete chat: Missing user ID or match ID');return;}const chatId=[user.id,matchUserId].sort().join('_');await AsyncStorage.removeItem(`chat_${chatId}`);const updatedMatches=matches.filter(match=>match.userId!==matchUserId);setMatches(updatedMatches);await AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));if(socketRef.current&&socketRef.current.connected){if(!isBlocking){socketRef.current.emit('allowRematch',{userId:user.id,otherUserId:matchUserId});}socketRef.current.emit('chatDeleted',{userId:user.id,otherUserId:matchUserId,reason:isBlocking?'blocked':'deleted',allowRematch:!isBlocking});}console.log(`Chat deleted successfully. Rematch ${isBlocking?'not allowed':'allowed'}`);}catch(error){console.error('Error deleting chat:',error);}};useEffect(()=>{if(isLoggedIn&&user){if(inactivityCheckIntervalRef.current){clearInterval(inactivityCheckIntervalRef.current);}inactivityCheckIntervalRef.current=setInterval(async()=>{try{setMatches(prevMatches=>{const now=new Date();const updatedMatches=prevMatches.filter(match=>{if(match.hasActivity)return true;const matchCreatedAt=new Date(match.createdAt||now);const inactiveThreshold=60*1000;const isInactive=now-matchCreatedAt>inactiveThreshold;if(isInactive){const chatId=[user.id,match.userId].sort().join('_');AsyncStorage.removeItem(`chat_${chatId}`);if(socketRef.current&&socketRef.current.connected){socketRef.current.emit('allowRematch',{userId:user.id,otherUserId:match.userId});socketRef.current.emit('chatDeleted',{userId:user.id,otherUserId:match.userId,reason:'inactive',allowRematch:true});}return false;}return true;});if(updatedMatches.length!==prevMatches.length){AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));}return updatedMatches;});}catch(error){console.error('Error checking for inactive matches:',error);}},30000);return()=>{if(inactivityCheckIntervalRef.current){clearInterval(inactivityCheckIntervalRef.current);}};}},[isLoggedIn,user]);useEffect(()=>{if(isLoggedIn&&user&&serverAddress){socketRef.current=io(`http://${serverAddress}`);socketRef.current.on('connect',()=>{console.log('Connected to server');socketRef.current.emit('register',{userId:user.id,username:user.username});socketRef.current.emit('syncBlocked',{userId:user.id,blockedUsers:blockedUsers});socketRef.current.emit('resetMatchRestrictions',{userId:user.id});});socketRef.current.on('match',matchData=>{console.log('New match received:',matchData);if(!matchData||!matchData.userId){console.error('Received invalid match data:',matchData);return;}if(isUserBlocked(matchData.userId)){console.log('Ignoring match with blocked user:',matchData.userId);return;}setMatches(prev=>{const matchExists=prev.some(m=>m.userId===matchData.userId);if(matchExists){return prev;}const updatedMatch=Object.assign({},matchData,{createdAt:new Date().toISOString(),hasActivity:false});const updatedMatches=[...prev,updatedMatch];try{AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));}catch(e){console.error('Error saving matches:',e);}return updatedMatches;});});socketRef.current.on('navigate',({screen,params})=>{console.log(`Navigation requested to ${screen}:`,params);if(screen==='Match'){console.log('Match screen navigation prevented');return;}if(navigationRef.current&&screen){try{setTimeout(()=>{navigationRef.current.navigate(screen,params);},300);}catch(error){console.error('Navigation error:',error);}}else{console.error('Cannot navigate: Navigation ref or screen name missing');}});socketRef.current.on('message',messageData=>{console.log('New message received:',messageData);if(!messageData||!messageData.senderId||!messageData.receiverId){console.error('Received invalid message data:',messageData);return;}if(isUserBlocked(messageData.senderId)){console.log('Ignoring message from blocked user:',messageData.senderId);return;}saveMessageToStorage(messageData);if(messageData.receiverId===user.id&&messageData.senderId!==user.id){setMatches(prevMatches=>{const matchIndex=prevMatches.findIndex(m=>m.userId===messageData.senderId);let updatedMatches=[...prevMatches];if(matchIndex===-1){console.log(`Recreating match with user ${messageData.senderUsername} after receiving a message`);const newMatch={userId:messageData.senderId,username:messageData.senderUsername,createdAt:new Date().toISOString(),hasActivity:true,lastActivity:new Date().toISOString(),hasUnreadMessages:true};updatedMatches=[...prevMatches,newMatch];}else{const isInChatWithSender=isUserInChatWith(messageData.senderId);const updatedMatch=Object.assign({},prevMatches[matchIndex],{hasActivity:true,lastActivity:new Date().toISOString()});if(!isInChatWithSender){updatedMatch.hasUnreadMessages=true;}updatedMatches[matchIndex]=updatedMatch;}try{AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));}catch(e){console.error('Error saving updated matches:',e);}return updatedMatches;});}});socketRef.current.on('messagesRead',data=>{if(data.userId===user.id){setMatches(prevMatches=>{const matchIndex=prevMatches.findIndex(m=>m.userId===data.otherUserId);if(matchIndex===-1)return prevMatches;const updatedMatches=[...prevMatches];updatedMatches[matchIndex]=Object.assign({},updatedMatches[matchIndex],{hasUnreadMessages:false,hasActivity:true,lastActivity:new Date().toISOString()});try{AsyncStorage.setItem(`matches_${user.id}`,JSON.stringify(updatedMatches));}catch(e){console.error('Error saving updated matches:',e);}return updatedMatches;});}});socketRef.current.on('userBlockedYou',data=>{if(data.blockedUserId===user.id){const blockerMatch=matches.find(m=>m.userId===data.userId);if(blockerMatch){const blockerUsername=blockerMatch.username||'Someone';handleDeleteChat(data.userId,false);if(!isUserInChatWith(data.userId)){Alert.alert(\"Blocked\",`${blockerUsername} has blocked you.`,[{text:\"OK\"}]);}}}});socketRef.current.on('disconnect',()=>{console.log('Disconnected from server');});socketRef.current.on('error',error=>{console.error('Socket error:',error);});return()=>{if(socketRef.current){socketRef.current.disconnect();}};}},[isLoggedIn,user,serverAddress,blockedUsers]);useEffect(()=>{let subscription;let lastShakeTime=0;let shakeCount=0;const startAccelerometer=async()=>{try{const isAvailable=await Accelerometer.isAvailableAsync();if(!isAvailable){console.log(\"Accelerometer not available on this device\");return;}Accelerometer.setUpdateInterval(100);subscription=Accelerometer.addListener(accelerometerData=>{const{x,y,z}=accelerometerData;const acceleration=Math.sqrt(x*x+y*y+z*z);const now=Date.now();if(acceleration>1.8){shakeCount++;if(shakeCount>=2&&now-lastShakeTime<1000){console.log(\"Physical shake detected:\",acceleration);shakeCount=0;lastShakeTime=now;handleShake();}else if(now-lastShakeTime>1000){shakeCount=1;lastShakeTime=now;}}});console.log(\"Accelerometer listener set up successfully\");}catch(error){console.error(\"Error setting up accelerometer:\",error);}};if(isLoggedIn){console.log(\"Starting accelerometer setup\");startAccelerometer();}return()=>{if(subscription){console.log(\"Removing accelerometer listener\");subscription.remove();}};},[isLoggedIn]);const setupLocation=async()=>{try{console.log(\"Starting location setup\");const providerStatus=await Location.getProviderStatusAsync();if(!providerStatus.locationServicesEnabled){console.error(\"Location services are disabled at device level\");Alert.alert(\"Location Services Disabled\",\"Location services are turned off on your device. Please enable them in your device settings.\",[{text:\"OK\"},{text:\"Open Settings\",onPress:()=>{if(false){Linking.openURL('app-settings:');}else{Linking.openSettings();}}}]);return;}const{status}=await Location.getForegroundPermissionsAsync();if(status!=='granted'){console.log(\"Location permission not granted, requesting...\");const{status:newStatus}=await Location.requestForegroundPermissionsAsync();if(newStatus!=='granted'){console.error(\"Location permission denied by user\");Alert.alert(\"Location Required\",\"Shake & Match requires location access to find nearby matches. Without location, the app cannot function properly.\",[{text:\"OK\"},{text:\"Open Settings\",onPress:()=>{if(false){Linking.openURL('app-settings:');}else{Linking.openSettings();}}}]);return;}}try{console.log(\"Getting initial location...\");const initialLocation=await Location.getCurrentPositionAsync({accuracy:Location.Accuracy.Balanced});const formattedLocation={latitude:initialLocation.coords.latitude,longitude:initialLocation.coords.longitude};console.log(\"Initial location obtained:\",JSON.stringify(formattedLocation));setLocation(formattedLocation);await AsyncStorage.setItem('lastKnownLocation',JSON.stringify(formattedLocation));if(socketRef.current&&socketRef.current.connected&&user){socketRef.current.emit('updateLocation',{userId:user.id,location:formattedLocation});}}catch(initLocError){console.error(\"Error getting initial location:\",initLocError);try{const storedLocation=await AsyncStorage.getItem('lastKnownLocation');if(storedLocation){const parsedLocation=JSON.parse(storedLocation);console.log(\"Using stored location:\",JSON.stringify(parsedLocation));setLocation(parsedLocation);}}catch(storageError){console.error(\"Error reading stored location:\",storageError);}}console.log(\"Starting location watcher...\");locationWatcher=await Location.watchPositionAsync({accuracy:Location.Accuracy.High,distanceInterval:10,timeInterval:5000},location=>{const newLocation={latitude:location.coords.latitude,longitude:location.coords.longitude};console.log(\"Location updated:\",JSON.stringify(newLocation));setLocation(newLocation);AsyncStorage.setItem('lastKnownLocation',JSON.stringify(newLocation)).catch(err=>console.error(\"Error saving location:\",err));if(socketRef.current&&socketRef.current.connected&&user){socketRef.current.emit('updateLocation',{userId:user.id,location:newLocation});}});console.log(\"Location watch setup complete\");}catch(error){console.error('Error setting up location:',error);Alert.alert(\"Location Error\",\"There was a problem accessing your location. Please make sure location services are enabled and try again.\");}};const handleShake=()=>{if(shakeDetected||!user||!user.id){const reason=shakeDetected?\"already shaking\":\"missing user ID\";console.log(`Shake ignored - ${reason}`);return;}console.log(\"Shake handler triggered\");const getLatestLocation=async()=>{try{const storedLocation=await AsyncStorage.getItem('lastKnownLocation');if(storedLocation){const parsedLocation=JSON.parse(storedLocation);console.log(\"Retrieved stored location:\",JSON.stringify(parsedLocation));setLocation(parsedLocation);return processShake(parsedLocation);}}catch(error){console.log(\"No stored location available, trying GPS\");}if(!location){try{console.log(\"Getting fresh location...\");const currentLocation=await Location.getCurrentPositionAsync({accuracy:Location.Accuracy.Balanced});const formattedLocation={latitude:currentLocation.coords.latitude,longitude:currentLocation.coords.longitude};console.log(\"Retrieved current location:\",JSON.stringify(formattedLocation));await AsyncStorage.setItem('lastKnownLocation',JSON.stringify(formattedLocation));setLocation(formattedLocation);return processShake(formattedLocation);}catch(locationError){console.error(\"Error getting current location:\",locationError);Alert.alert(\"Location Required\",\"Please make sure location services are enabled with high accuracy.\",[{text:\"OK\"},{text:\"Open Settings\",onPress:()=>{if(false){Linking.openURL('app-settings:');}else{Linking.openSettings();}}}]);setShakeDetected(false);return;}}return processShake(location);};getLatestLocation();};const processShake=currentLocation=>{setShakeDetected(true);console.log(`Shake initiated by user: ${user.username}, ID: ${user.id}`);if(!socketRef.current||!socketRef.current.connected){console.error(\"Socket not connected. Attempting to reconnect...\");if(serverAddress){socketRef.current=io(`http://${serverAddress}`);socketRef.current.on('connect',()=>{console.log('Socket reconnected, registering user');socketRef.current.emit('register',{userId:user.id,username:user.username});});}Alert.alert(\"Connection Issue\",\"There was a problem connecting to the server. Please try again.\");setShakeDetected(false);return;}if(isLoggedIn&&socketRef.current&&socketRef.current.connected){console.log(\"Sending shake event to server\");socketRef.current.emit('shake',{userId:user.id,username:user.username,location:currentLocation,maxDistance,timestamp:new Date().toISOString(),blockedUsers,rematchEnabled:true,minAge:minAge,maxAge:maxAge});console.log(`Shake event sent with userID: ${user.id}, username: ${user.username}, location:`,JSON.stringify(currentLocation));}else{console.log(\"Cannot send shake event:\",{isLoggedIn,socketConnected:socketRef.current&&socketRef.current.connected,hasLocation:!!currentLocation});}setTimeout(()=>{console.log(\"Resetting shake detection state\");setShakeDetected(false);},2000);};const handleLogin=async(userData,server)=>{console.log('=== APP.JS HANDLE LOGIN CALLED ===');console.log('userData received:',JSON.stringify(userData));console.log('server received:',server);if(!userData){console.error('Error: userData is null or undefined');Alert.alert('Login Error','Invalid user data received. Please try again.');return;}if(typeof userData!=='object'){console.error('Error: userData is not an object, type:',typeof userData);Alert.alert('Login Error','Received invalid user data format. Please try again.');return;}const userId=userData.id;if(!userId){console.error('Error: No user ID in userData:',userData);Alert.alert('Login Error','User ID missing from login data. Please try again.');return;}console.log('User ID validation passed:',userId);const user={id:userId,username:userData.username||'User'};console.log('Setting user state to:',JSON.stringify(user));setUser(user);console.log('Setting isLoggedIn to true');setIsLoggedIn(true);console.log('Setting serverAddress to:',server);setServerAddress(server);try{const storageData=JSON.stringify(user);console.log('Storing in AsyncStorage:',storageData);try{await AsyncStorage.setItem('user',storageData);console.log('AsyncStorage: user data stored successfully');}catch(userError){console.error('AsyncStorage: Error storing user data:',userError);}try{await AsyncStorage.setItem('serverAddress',server);console.log('AsyncStorage: server address stored successfully');}catch(serverError){console.error('AsyncStorage: Error storing server address:',serverError);}try{const storedUser=await AsyncStorage.getItem('user');console.log('Verification - stored user data:',storedUser);}catch(verifyError){console.error('Error verifying stored user data:',verifyError);}}catch(error){console.error('Overall error in storing login data:',error);}console.log('=== LOGIN PROCESS COMPLETED ===');};const handleLogout=async()=>{if(socketRef.current){socketRef.current.disconnect();}const currentServer=serverAddress;setUser(null);setIsLoggedIn(false);setMatches([]);setBlockedUsers([]);try{await AsyncStorage.removeItem('user');setServerAddress(currentServer);}catch(error){console.error('Error during logout:',error);}};const updateMaxDistance=async distance=>{setMaxDistance(distance);try{await AsyncStorage.setItem('maxDistance',distance.toString());}catch(error){console.error('Error saving max distance:',error);}};return _jsxs(View,{style:{flex:1},children:[_jsx(StatusBar,{barStyle:\"dark-content\",backgroundColor:\"#fff\"}),_jsx(NavigationContainer,{ref:navigationRef,children:_jsx(Stack.Navigator,{initialRouteName:isLoggedIn?\"Home\":\"Login\",screenOptions:{headerShown:true,headerStyle:{backgroundColor:'#fff',elevation:0,shadowOpacity:0,borderBottomWidth:1,borderBottomColor:'#eee'},headerTitleStyle:{fontWeight:'600',color:'#4e9af1'},headerTintColor:'#4e9af1'},children:!isLoggedIn?_jsxs(_Fragment,{children:[_jsx(Stack.Screen,{name:\"Login\",options:{title:\"Welcome\",headerShown:false},children:props=>_jsx(LoginScreen,Object.assign({},props,{onLogin:handleLogin,serverAddress:serverAddress}))}),_jsx(Stack.Screen,{name:\"Register\",options:{title:\"Create Account\",headerShown:false},children:props=>_jsx(RegisterScreen,Object.assign({},props,{serverAddress:serverAddress}))})]}):_jsxs(_Fragment,{children:[_jsx(Stack.Screen,{name:\"Home\",options:{title:\"Shake & Match\"},children:props=>_jsx(HomeScreen,Object.assign({},props,{user:user,shakeDetected:shakeDetected,matches:matches,onShake:handleShake,onDeleteMatch:handleDeleteChat,onBlockUser:handleBlockUser,serverAddress:serverAddress}))}),_jsx(Stack.Screen,{name:\"Settings\",children:props=>_jsx(SettingsScreen,Object.assign({},props,{maxDistance:maxDistance,minAge:minAge,maxAge:maxAge,blockedUsers:blockedUsers,onUpdateMaxDistance:updateMaxDistance,onUpdateAgeRange:updateAgeRange,onUnblockUser:handleUnblockUser,onLogout:handleLogout}))}),_jsx(Stack.Screen,{name:\"Chat\",children:props=>_jsx(ChatScreen,Object.assign({},props,{socket:socketRef.current,user:user,onDeleteChat:handleDeleteChat,onBlockUser:handleBlockUser}))}),_jsx(Stack.Screen,{name:\"Match\",children:props=>_jsx(MatchScreen,Object.assign({},props,{serverAddress:serverAddress}))}),_jsx(Stack.Screen,{name:\"ViewProfile\",component:ViewProfileScreen}),_jsx(Stack.Screen,{name:\"ProfileScreen\",children:props=>_jsx(ProfileScreen,Object.assign({},props,{user:user,serverAddress:serverAddress,onUpdateProfile:handleUpdateProfile,isInitialSetup:props.route.params?.isInitialSetup||false}))})]})})})]});}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "NavigationContainer", "createStackNavigator", "Accelerometer", "Location", "io", "AsyncStorage", "StatusBar", "View", "<PERSON><PERSON>", "Linking", "Platform", "LoginScreen", "RegisterScreen", "HomeScreen", "SettingsScreen", "ChatScreen", "MatchScreen", "ViewProfileScreen", "ProfileScreen", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "<PERSON><PERSON>", "App", "isLoggedIn", "setIsLoggedIn", "user", "setUser", "shakeDetected", "setShakeDetected", "location", "setLocation", "maxDistance", "setMaxDistance", "matches", "setMatches", "blockedUsers", "setBlockedUsers", "socketRef", "serverAddress", "setServerAddress", "navigationRef", "inactivityCheckIntervalRef", "userProfile", "setUserProfile", "minAge", "setMinAge", "maxAge", "setMaxAge", "checkLoginStatus", "userJSON", "getItem", "storedServer", "storedMaxDistance", "storedMinAge", "storedMaxAge", "loginSuccessful", "console", "log", "userData", "JSON", "parse", "id", "error", "removeItem", "response", "fetch", "method", "headers", "data", "json", "ok", "exists", "username", "validationError", "parseError", "parseFloat", "parseInt", "updateAgeRange", "min", "max", "setItem", "toString", "loadUserData", "storedMatches", "storedBlockedUsers", "storedProfile", "checkProfileCompletion", "profileData", "isProfileComplete", "description", "age", "images", "length", "current", "setTimeout", "reset", "index", "routes", "name", "params", "isInitialSetup", "handleUpdateProfile", "isUserBlocked", "userId", "includes", "isUserInChatWith", "currentRoute", "getCurrentRoute", "match", "e", "handleBlockUser", "updatedBlockedUsers", "stringify", "handleDeleteChat", "connected", "emit", "blockedUserId", "handleUnblockUser", "filter", "unblockedUserId", "saveMessageToStorage", "messageData", "senderId", "receiverId", "chatId", "sort", "join", "storedMessagesJSON", "storedMessages", "formattedMessage", "Date", "now", "text", "timestamp", "toISOString", "messageExists", "some", "msg", "updatedMessages", "updateMatchLastActivity", "matchUserId", "prevMatches", "updatedMatches", "map", "Object", "assign", "lastActivity", "hasActivity", "isBlocking", "otherUserId", "reason", "allowRematch", "clearInterval", "setInterval", "matchCreatedAt", "createdAt", "inactiveThreshold", "isInactive", "on", "matchData", "prev", "matchExists", "m", "updatedMatch", "screen", "navigate", "matchIndex", "findIndex", "senderUsername", "newMatch", "hasUnreadMessages", "isInChatWithSender", "blockerMatch", "find", "blockerUsername", "alert", "disconnect", "subscription", "lastShakeTime", "shakeCount", "startAccelerometer", "isAvailable", "isAvailableAsync", "setUpdateInterval", "addListener", "accelerometerData", "x", "y", "z", "acceleration", "Math", "sqrt", "handleShake", "remove", "setupLocation", "providerStatus", "getProviderStatusAsync", "locationServicesEnabled", "onPress", "openURL", "openSettings", "status", "getForegroundPermissionsAsync", "newStatus", "requestForegroundPermissionsAsync", "initialLocation", "getCurrentPositionAsync", "accuracy", "Accuracy", "Balanced", "formattedLocation", "latitude", "coords", "longitude", "initLocError", "storedLocation", "parsedLocation", "storageError", "locationWatcher", "watchPositionAsync", "High", "distanceInterval", "timeInterval", "newLocation", "catch", "err", "getLatestLocation", "processShake", "currentLocation", "locationError", "rematchEnabled", "socketConnected", "hasLocation", "handleLogin", "server", "storageData", "userError", "serverError", "storedUser", "verifyError", "handleLogout", "currentServer", "updateMaxDistance", "distance", "style", "flex", "children", "barStyle", "backgroundColor", "ref", "Navigator", "initialRouteName", "screenOptions", "headerShown", "headerStyle", "elevation", "shadowOpacity", "borderBottomWidth", "borderBottomColor", "headerTitleStyle", "fontWeight", "color", "headerTintColor", "Screen", "options", "title", "props", "onLogin", "onShake", "onDeleteMatch", "onBlockUser", "onUpdateMaxDistance", "onUpdateAgeRange", "onUnblockUser", "onLogout", "socket", "onDeleteChat", "component", "onUpdateProfile", "route"], "sources": ["/Users/<USER>/Desktop/Shake2/App.js"], "sourcesContent": ["// App.js - With Login as first screen and fixed navigation\nimport React, { useState, useEffect, useRef } from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport { Accelerometer } from 'expo-sensors';\nimport * as Location from 'expo-location';\nimport { io } from 'socket.io-client';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { StatusBar, View, Alert, Linking, Platform } from 'react-native';\nimport 'react-native-gesture-handler';\n\n// Import screens\nimport LoginScreen from './screens/LoginScreen';\nimport RegisterScreen from './screens/RegisterScreen';\nimport HomeScreen from './screens/HomeScreen';\nimport SettingsScreen from './screens/SettingsScreen';\nimport ChatScreen from './screens/ChatScreen';\nimport MatchScreen from './screens/MatchScreen';\nimport ViewProfileScreen from './screens/ViewProfileScreen';\nimport ProfileScreen from './screens/ProfileScreen';\n\nconst Stack = createStackNavigator();\n\nexport default function App() {\n  // State variables and refs remain the same\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState(null);\n  const [shakeDetected, setShakeDetected] = useState(false);\n  const [location, setLocation] = useState(null);\n  const [maxDistance, setMaxDistance] = useState(5); // Default 5km\n  const [matches, setMatches] = useState([]);\n  const [blockedUsers, setBlockedUsers] = useState([]);\n  const socketRef = useRef(null);\n  const [serverAddress, setServerAddress] = useState('');\n  const navigationRef = useRef(null);\n  const inactivityCheckIntervalRef = useRef(null);\n  const [userProfile, setUserProfile] = useState(null);\n  const [minAge, setMinAge] = useState(18);  // Default min age\n  const [maxAge, setMaxAge] = useState(100);  // Default max age\n  \n  // Check for stored credentials on app start with database validation\n  useEffect(() => {\n    const checkLoginStatus = async () => {\n      try {\n        const userJSON = await AsyncStorage.getItem('user');\n        const storedServer = await AsyncStorage.getItem('serverAddress');\n        const storedMaxDistance = await AsyncStorage.getItem('maxDistance');\n        const storedMinAge = await AsyncStorage.getItem('minAge');\n        const storedMaxAge = await AsyncStorage.getItem('maxAge');\n\n        let loginSuccessful = false;\n\n        if (userJSON && storedServer) {\n          console.log('Found stored user data:', userJSON);\n          \n          try {\n            const userData = JSON.parse(userJSON);\n            \n            // Verify the user data has a valid ID\n            if (!userData.id) {\n              console.error('Stored user data missing ID:', userData);\n              // Don't log in with invalid data\n              await AsyncStorage.removeItem('user');\n            } else {\n              // Validate that the user still exists in the database\n              try {\n                const response = await fetch(`http://${storedServer}/api/debug/user/${userData.id}`, {\n                  method: 'GET',\n                  headers: {\n                    'Content-Type': 'application/json',\n                  },\n                });\n                \n                const data = await response.json();\n                \n                if (!response.ok || !data.exists) {\n                  // User doesn't exist\n                  console.log('User no longer exists in database, clearing credentials');\n                  await AsyncStorage.removeItem('user');\n                } else {\n                  // User exists, proceed with login\n                  console.log('User validation successful');\n                  console.log('Logging in with stored credentials:', userData.username, userData.id);\n                  setUser(userData);\n                  setIsLoggedIn(true);\n                  setServerAddress(storedServer);\n                  loginSuccessful = true;\n                }\n              } catch (validationError) {\n                console.error('Error validating user:', validationError);\n                // On error, be cautious and clear credentials\n                await AsyncStorage.removeItem('user');\n              }\n            }\n          } catch (parseError) {\n            console.error('Error parsing stored user data:', parseError);\n            await AsyncStorage.removeItem('user');\n          }\n        } else {\n          console.log('No stored user data found or missing server address');\n        }\n        \n        if (storedServer) {\n          setServerAddress(storedServer);\n        }\n        \n        if (storedMaxDistance) {\n          setMaxDistance(parseFloat(storedMaxDistance) || 5);\n        }\n        \n        if (storedMinAge) {\n          setMinAge(parseInt(storedMinAge) || 18);\n        }\n        \n        if (storedMaxAge) {\n          setMaxAge(parseInt(storedMaxAge) || 100);\n        }\n        \n        // Ensure isLoggedIn is false if login wasn't successful\n        if (!loginSuccessful) {\n          setIsLoggedIn(false);\n          setUser(null);\n        }\n      } catch (error) {\n        console.error('Error checking login status:', error);\n        setIsLoggedIn(false);\n        setUser(null);\n      }\n    };\n    \n    checkLoginStatus();\n  }, []);\n\n  //handle updating age range settings\n  const updateAgeRange = async (min, max) => {\n    setMinAge(min);\n    setMaxAge(max);\n    try {\n      await AsyncStorage.setItem('minAge', min.toString());\n      await AsyncStorage.setItem('maxAge', max.toString());\n    } catch (error) {\n      console.error('Error saving age range settings:', error);\n    }\n  };\n\n  // Load stored matches and blocked users\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        if (user?.id) {\n          // Load matches\n          const storedMatches = await AsyncStorage.getItem(`matches_${user.id}`);\n          if (storedMatches) {\n            setMatches(JSON.parse(storedMatches));\n          }\n          \n          // Load blocked users\n          const storedBlockedUsers = await AsyncStorage.getItem(`blocked_${user.id}`);\n          if (storedBlockedUsers) {\n            setBlockedUsers(JSON.parse(storedBlockedUsers));\n          }\n          // Loading user profile\n          const storedProfile = await AsyncStorage.getItem(`profile_${user.id}`);\n          if (storedProfile) {\n            setUserProfile(JSON.parse(storedProfile));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    };\n    \n    if (isLoggedIn && user) {\n      loadUserData();\n    }\n  }, [isLoggedIn, user]);\n\n  useEffect(() => {\n    const checkProfileCompletion = async () => {\n      if (isLoggedIn && user?.id) {\n        try {\n          // Get user profile\n          const storedProfile = await AsyncStorage.getItem(`profile_${user.id}`);\n          let profileData = null;\n          \n          if (storedProfile) {\n            profileData = JSON.parse(storedProfile);\n          }\n          \n          // Check if profile is complete\n          const isProfileComplete = \n            profileData && \n            profileData.description && \n            profileData.age && \n            profileData.images && \n            profileData.images.length >= 3;\n          \n          // If profile is incomplete, force navigation to profile screen\n          if (!isProfileComplete && navigationRef.current) {\n            console.log('Profile incomplete, redirecting to profile setup');\n            // Use a timeout to ensure navigation is ready\n            setTimeout(() => {\n              navigationRef.current.reset({\n                index: 0,\n                routes: [{ \n                  name: 'ProfileScreen', \n                  params: { isInitialSetup: true }\n                }]\n              });\n            }, 500);\n          }\n        } catch (error) {\n          console.error('Error checking profile completion:', error);\n        }\n      }\n    };\n    \n    checkProfileCompletion();\n  }, [isLoggedIn, user]);\n\n  const handleUpdateProfile = (profileData) => {\n    setUserProfile(profileData);\n  };\n  \n  // Helper to check if a user is blocked\n  const isUserBlocked = (userId) => {\n    return blockedUsers.includes(userId);\n  };\n\n  // Helper to check if the user is currently in a chat with someone\n  const isUserInChatWith = (userId) => {\n    if (!navigationRef.current) return false;\n    \n    try {\n      const currentRoute = navigationRef.current.getCurrentRoute();\n      if (currentRoute?.name === 'Chat' && currentRoute?.params?.match?.userId === userId) {\n        return true;\n      }\n    } catch (e) {\n      // If there's any error reading the route, assume user is not in chat\n      console.error('Error checking current route:', e);\n    }\n    \n    return false;\n  };\n\n  // Function to block a user\n  const handleBlockUser = async (userId) => {\n    try {\n      if (!user?.id || !userId) {\n        console.error('Cannot block user: Missing user ID or target ID');\n        return;\n      }\n      \n      // First, check if already blocked\n      if (isUserBlocked(userId)) {\n        return; // Already blocked\n      }\n      \n      // Add to blocked users list\n      const updatedBlockedUsers = [...blockedUsers, userId];\n      setBlockedUsers(updatedBlockedUsers);\n      \n      // Save blocked users to AsyncStorage\n      await AsyncStorage.setItem(`blocked_${user.id}`, JSON.stringify(updatedBlockedUsers));\n      \n      // Delete any existing chat with this user\n      await handleDeleteChat(userId, true);\n      \n      // Notify server of block\n      if (socketRef.current && socketRef.current.connected) {\n        socketRef.current.emit('blockUser', {\n          userId: user.id,\n          blockedUserId: userId\n        });\n      }\n      \n      console.log('User blocked successfully');\n    } catch (error) {\n      console.error('Error blocking user:', error);\n    }\n  };\n\n  // Function to unblock a user\n  const handleUnblockUser = async (userId) => {\n    try {\n      if (!user?.id || !userId) {\n        console.error('Cannot unblock user: Missing user ID or target ID');\n        return;\n      }\n      \n      // Remove from blocked users list\n      const updatedBlockedUsers = blockedUsers.filter(id => id !== userId);\n      setBlockedUsers(updatedBlockedUsers);\n      \n      // Save updated blocked users to AsyncStorage\n      await AsyncStorage.setItem(`blocked_${user.id}`, JSON.stringify(updatedBlockedUsers));\n      \n      // Notify server of unblock\n      if (socketRef.current && socketRef.current.connected) {\n        socketRef.current.emit('unblockUser', {\n          userId: user.id,\n          unblockedUserId: userId\n        });\n      }\n      \n      console.log('User unblocked successfully');\n    } catch (error) {\n      console.error('Error unblocking user:', error);\n    }\n  };\n\n  // Function to save messages to AsyncStorage\n  const saveMessageToStorage = async (messageData) => {\n    try {\n      if (!messageData || !messageData.senderId || !messageData.receiverId) {\n        console.error('Invalid message data for storage');\n        return;\n      }\n      \n      // Create a unique chat ID by sorting user IDs\n      const chatId = [messageData.senderId, messageData.receiverId].sort().join('_');\n      \n      // Get existing messages for this chat\n      const storedMessagesJSON = await AsyncStorage.getItem(`chat_${chatId}`);\n      let storedMessages = [];\n      \n      if (storedMessagesJSON) {\n        storedMessages = JSON.parse(storedMessagesJSON);\n      }\n      \n      // Format the message to match the structure used in ChatScreen\n      const formattedMessage = {\n        id: messageData.id || Date.now().toString(),\n        text: messageData.text,\n        senderId: messageData.senderId,\n        timestamp: messageData.timestamp || new Date().toISOString()\n      };\n      \n      // Check if message already exists\n      const messageExists = storedMessages.some(msg => msg.id === formattedMessage.id);\n      if (messageExists) {\n        return;\n      }\n      \n      // Add the new message to the existing messages\n      const updatedMessages = [...storedMessages, formattedMessage];\n      \n      // Save updated messages back to AsyncStorage\n      await AsyncStorage.setItem(`chat_${chatId}`, JSON.stringify(updatedMessages));\n      \n      // Update the match's lastActivity timestamp\n      updateMatchLastActivity(messageData.senderId === user.id ? messageData.receiverId : messageData.senderId);\n      \n      console.log('Message saved to storage successfully');\n    } catch (error) {\n      console.error('Error saving message to storage:', error);\n    }\n  };\n\n  // Function to update a match's last activity timestamp\n  const updateMatchLastActivity = (matchUserId) => {\n    setMatches(prevMatches => {\n      const updatedMatches = prevMatches.map(match => {\n        if (match.userId === matchUserId) {\n          return {\n            ...match,\n            lastActivity: new Date().toISOString(),\n            hasActivity: true\n          };\n        }\n        return match;\n      });\n      \n      // Save the updated matches to AsyncStorage\n      try {\n        if (user?.id) {\n          AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n        }\n      } catch (e) {\n        console.error('Error saving updated matches:', e);\n      }\n      \n      return updatedMatches;\n    });\n  };\n\n  // Function to handle deleting a chat\n  const handleDeleteChat = async (matchUserId, isBlocking = false) => {\n    try {\n      if (!user?.id || !matchUserId) {\n        console.error('Cannot delete chat: Missing user ID or match ID');\n        return;\n      }\n      \n      // 1. First, delete the chat messages\n      const chatId = [user.id, matchUserId].sort().join('_');\n      await AsyncStorage.removeItem(`chat_${chatId}`);\n      \n      // 2. Then, update the matches array to remove the deleted match\n      const updatedMatches = matches.filter(match => match.userId !== matchUserId);\n      setMatches(updatedMatches);\n      \n      // 3. Save the updated matches to AsyncStorage\n      await AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n      \n      // 4. CRITICAL: First, explicitly send an allowRematch event regardless of reason\n      if (socketRef.current && socketRef.current.connected) {\n        // Only if NOT blocking, send the explicit allow rematch event\n        if (!isBlocking) {\n          socketRef.current.emit('allowRematch', {\n            userId: user.id,\n            otherUserId: matchUserId\n          });\n        }\n        \n        // Then send the chatDeleted event with the appropriate allowRematch flag\n        socketRef.current.emit('chatDeleted', {\n          userId: user.id,\n          otherUserId: matchUserId,\n          reason: isBlocking ? 'blocked' : 'deleted',\n          allowRematch: !isBlocking // Allow rematch if NOT blocking\n        });\n      }\n      \n      console.log(`Chat deleted successfully. Rematch ${isBlocking ? 'not allowed' : 'allowed'}`);\n    } catch (error) {\n      console.error('Error deleting chat:', error);\n    }\n  };\n\n  // Set up interval to check for inactive matches\n  useEffect(() => {\n    if (isLoggedIn && user) {\n      // Clear any existing interval\n      if (inactivityCheckIntervalRef.current) {\n        clearInterval(inactivityCheckIntervalRef.current);\n      }\n      \n      // Set up a new interval to check for inactive matches\n      inactivityCheckIntervalRef.current = setInterval(async () => {\n        try {\n          // Check each match for inactivity\n          setMatches(prevMatches => {\n            const now = new Date();\n            const updatedMatches = prevMatches.filter(match => {\n              // Skip matches that already have activity\n              if (match.hasActivity) return true;\n              \n              // Check if match was created more than 1 minute ago\n              const matchCreatedAt = new Date(match.createdAt || now);\n              const inactiveThreshold = 60 * 1000; // 1 minute in milliseconds\n              const isInactive = (now - matchCreatedAt) > inactiveThreshold;\n              \n              // If inactive and no message activity, delete it\n              if (isInactive) {\n                // Delete chat messages\n                const chatId = [user.id, match.userId].sort().join('_');\n                AsyncStorage.removeItem(`chat_${chatId}`);\n                \n                // Notify server that the chat was auto-deleted BUT ALLOW REMATCH\n                if (socketRef.current && socketRef.current.connected) {\n                  // Send explicit allow rematch event first\n                  socketRef.current.emit('allowRematch', {\n                    userId: user.id,\n                    otherUserId: match.userId\n                  });\n                  \n                  // Then send chat deleted with allowRematch flag\n                  socketRef.current.emit('chatDeleted', {\n                    userId: user.id,\n                    otherUserId: match.userId,\n                    reason: 'inactive',\n                    allowRematch: true\n                  });\n                }\n                \n                return false; // Remove this match\n              }\n              \n              return true; // Keep the match\n            });\n            \n            // If matches changed, save them\n            if (updatedMatches.length !== prevMatches.length) {\n              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n            }\n            \n            return updatedMatches;\n          });\n        } catch (error) {\n          console.error('Error checking for inactive matches:', error);\n        }\n      }, 30000); // Check every 30 seconds\n      \n      // Clean up interval on unmount\n      return () => {\n        if (inactivityCheckIntervalRef.current) {\n          clearInterval(inactivityCheckIntervalRef.current);\n        }\n      };\n    }\n  }, [isLoggedIn, user]);\n\n  // Connect to socket when user logs in\n  useEffect(() => {\n    if (isLoggedIn && user && serverAddress) {\n      // Connect to the user's self-hosted server\n      socketRef.current = io(`http://${serverAddress}`);\n      \n      socketRef.current.on('connect', () => {\n        console.log('Connected to server');\n        // Register user with server\n        socketRef.current.emit('register', {\n          userId: user.id,\n          username: user.username\n        });\n        \n        // Send blocked users list to server\n        socketRef.current.emit('syncBlocked', {\n          userId: user.id,\n          blockedUsers: blockedUsers\n        });\n        \n        // NEW: Request server to clear any lingering match restrictions\n        socketRef.current.emit('resetMatchRestrictions', {\n          userId: user.id\n        });\n      });\n      \n      socketRef.current.on('match', (matchData) => {\n        console.log('New match received:', matchData);\n        \n        // Ensure match has all required fields\n        if (!matchData || !matchData.userId) {\n          console.error('Received invalid match data:', matchData);\n          return;\n        }\n        \n        // Check if user is blocked\n        if (isUserBlocked(matchData.userId)) {\n          console.log('Ignoring match with blocked user:', matchData.userId);\n          return;\n        }\n        \n        setMatches(prev => {\n          // Check if match already exists\n          const matchExists = prev.some(m => m.userId === matchData.userId);\n          if (matchExists) {\n            return prev;\n          }\n          \n          // Add createdAt and hasActivity fields to track engagement\n          const updatedMatch = {\n            ...matchData,\n            createdAt: new Date().toISOString(),\n            hasActivity: false\n          };\n          \n          const updatedMatches = [...prev, updatedMatch];\n          \n          // Save matches to AsyncStorage\n          try {\n            AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n          } catch (e) {\n            console.error('Error saving matches:', e);\n          }\n          \n          return updatedMatches;\n        });\n      });\n      \n// Handle server-initiated navigation\nsocketRef.current.on('navigate', ({ screen, params }) => {\n  console.log(`Navigation requested to ${screen}:`, params);\n  \n  // Add this check to prevent Match screen navigation\n  if (screen === 'Match') {\n    console.log('Match screen navigation prevented');\n    return; // Skip navigation to Match screen\n  }\n  \n  if (navigationRef.current && screen) {\n    try {\n      // Delay navigation slightly to ensure match data is stored\n      setTimeout(() => {\n        navigationRef.current.navigate(screen, params);\n      }, 300);\n    } catch (error) {\n      console.error('Navigation error:', error);\n    }\n  } else {\n    console.error('Cannot navigate: Navigation ref or screen name missing');\n  }\n});\n      \n      socketRef.current.on('message', (messageData) => {\n        console.log('New message received:', messageData);\n        \n        // Validate message data\n        if (!messageData || !messageData.senderId || !messageData.receiverId) {\n          console.error('Received invalid message data:', messageData);\n          return;\n        }\n        \n        // Check if message is from a blocked user\n        if (isUserBlocked(messageData.senderId)) {\n          console.log('Ignoring message from blocked user:', messageData.senderId);\n          return;\n        }\n        \n        // Save the message to AsyncStorage regardless of whether user is in chat\n        saveMessageToStorage(messageData);\n        \n        // Only process further if message is coming TO the current user\n        // and not FROM the current user\n        if (messageData.receiverId === user.id && messageData.senderId !== user.id) {\n          setMatches(prevMatches => {\n            // Find the match for this message\n            const matchIndex = prevMatches.findIndex(m => m.userId === messageData.senderId);\n            \n            let updatedMatches = [...prevMatches];\n            \n            if (matchIndex === -1) {\n              // This is a message from someone not in our matches list\n              // We should recreate the match if it was deleted (and not blocked)\n              console.log(`Recreating match with user ${messageData.senderUsername} after receiving a message`);\n              \n              // Create a new match object\n              const newMatch = {\n                userId: messageData.senderId,\n                username: messageData.senderUsername,\n                createdAt: new Date().toISOString(),\n                hasActivity: true,\n                lastActivity: new Date().toISOString(),\n                hasUnreadMessages: true\n              };\n              \n              // Add it to matches array\n              updatedMatches = [...prevMatches, newMatch];\n            } else {\n              // Update existing match\n              const isInChatWithSender = isUserInChatWith(messageData.senderId);\n              \n              // Create updated match object with activity flag set\n              const updatedMatch = {\n                ...prevMatches[matchIndex],\n                hasActivity: true,\n                lastActivity: new Date().toISOString()\n              };\n              \n              // If user is not in chat with this sender, mark as unread\n              if (!isInChatWithSender) {\n                updatedMatch.hasUnreadMessages = true;\n              }\n              \n              updatedMatches[matchIndex] = updatedMatch;\n            }\n            \n            // Save updated matches\n            try {\n              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n            } catch (e) {\n              console.error('Error saving updated matches:', e);\n            }\n            \n            return updatedMatches;\n          });\n        }\n      });\n      \n      // Handle when unread status is cleared from ChatScreen\n      socketRef.current.on('messagesRead', (data) => {\n        if (data.userId === user.id) {\n          setMatches(prevMatches => {\n            const matchIndex = prevMatches.findIndex(m => m.userId === data.otherUserId);\n            if (matchIndex === -1) return prevMatches;\n            \n            const updatedMatches = [...prevMatches];\n            updatedMatches[matchIndex] = {\n              ...updatedMatches[matchIndex],\n              hasUnreadMessages: false,\n              hasActivity: true,\n              lastActivity: new Date().toISOString()\n            };\n            \n            // Save updated matches\n            try {\n              AsyncStorage.setItem(`matches_${user.id}`, JSON.stringify(updatedMatches));\n            } catch (e) {\n              console.error('Error saving updated matches:', e);\n            }\n            \n            return updatedMatches;\n          });\n        }\n      });\n      \n      socketRef.current.on('userBlockedYou', (data) => {\n        if (data.blockedUserId === user.id) {\n          // Find the match info for this user\n          const blockerMatch = matches.find(m => m.userId === data.userId);\n          if (blockerMatch) {\n            const blockerUsername = blockerMatch.username || 'Someone';\n            \n            // Remove this match from the list since they blocked the user\n            handleDeleteChat(data.userId, false);\n            \n            // Show a notification if not currently in chat with this user\n            if (!isUserInChatWith(data.userId)) {\n              Alert.alert(\n                \"Blocked\",\n                `${blockerUsername} has blocked you.`,\n                [{ text: \"OK\" }]\n              );\n            }\n          }\n        }\n      });\n\n      // Handle disconnect\n      socketRef.current.on('disconnect', () => {\n        console.log('Disconnected from server');\n      });\n      \n      // Handle errors\n      socketRef.current.on('error', (error) => {\n        console.error('Socket error:', error);\n      });\n      \n      return () => {\n        if (socketRef.current) {\n          socketRef.current.disconnect();\n        }\n      };\n    }\n  }, [isLoggedIn, user, serverAddress, blockedUsers]);\n\n  // Set up accelerometer for shake detection with improved sensitivity\n  useEffect(() => {\n    let subscription;\n    let lastShakeTime = 0;\n    let shakeCount = 0;\n    \n    const startAccelerometer = async () => {\n      try {\n        const isAvailable = await Accelerometer.isAvailableAsync();\n        \n        if (!isAvailable) {\n          console.log(\"Accelerometer not available on this device\");\n          return;\n        }\n        \n        // Set update interval to 100ms for better shake detection\n        Accelerometer.setUpdateInterval(100);\n        \n        subscription = Accelerometer.addListener(accelerometerData => {\n          const { x, y, z } = accelerometerData;\n          const acceleration = Math.sqrt(x*x + y*y + z*z);\n          const now = Date.now();\n          \n          // Check if acceleration exceeds shake threshold\n          if (acceleration > 1.8) { // Lower threshold for more sensitivity\n            shakeCount++;\n            \n            // If we've detected 2+ shakes in quick succession\n            if (shakeCount >= 2 && (now - lastShakeTime) < 1000) {\n              console.log(\"Physical shake detected:\", acceleration);\n              // Reset shake count and time\n              shakeCount = 0;\n              lastShakeTime = now;\n              \n              // Call the same handleShake function\n              handleShake();\n            } else if ((now - lastShakeTime) > 1000) {\n              // If it's been more than 1 second, reset count but record time\n              shakeCount = 1;\n              lastShakeTime = now;\n            }\n          }\n        });\n        \n        console.log(\"Accelerometer listener set up successfully\");\n      } catch (error) {\n        console.error(\"Error setting up accelerometer:\", error);\n      }\n    };\n    \n    if (isLoggedIn) {\n      console.log(\"Starting accelerometer setup\");\n      startAccelerometer();\n    }\n    \n    return () => {\n      if (subscription) {\n        console.log(\"Removing accelerometer listener\");\n        subscription.remove();\n      }\n    };\n  }, [isLoggedIn]);\n\n  // Set up location services with enhanced error handling\n  const setupLocation = async () => {\n    try {\n      console.log(\"Starting location setup\");\n      \n      // First check if location services are enabled at the device level\n      const providerStatus = await Location.getProviderStatusAsync();\n      if (!providerStatus.locationServicesEnabled) {\n        console.error(\"Location services are disabled at device level\");\n        Alert.alert(\n          \"Location Services Disabled\",\n          \"Location services are turned off on your device. Please enable them in your device settings.\",\n          [\n            { text: \"OK\" },\n            { \n              text: \"Open Settings\", \n              onPress: () => {\n                if (Platform.OS === 'ios') {\n                  Linking.openURL('app-settings:');\n                } else {\n                  Linking.openSettings();\n                }\n              }\n            }\n          ]\n        );\n        return;\n      }\n      \n      // Check if app has location permission\n      const { status } = await Location.getForegroundPermissionsAsync();\n      \n      if (status !== 'granted') {\n        console.log(\"Location permission not granted, requesting...\");\n        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();\n        \n        if (newStatus !== 'granted') {\n          console.error(\"Location permission denied by user\");\n          Alert.alert(\n            \"Location Required\",\n            \"Shake & Match requires location access to find nearby matches. Without location, the app cannot function properly.\",\n            [\n              { text: \"OK\" },\n              { \n                text: \"Open Settings\", \n                onPress: () => {\n                  if (Platform.OS === 'ios') {\n                    Linking.openURL('app-settings:');\n                  } else {\n                    Linking.openSettings();\n                  }\n                } \n              }\n            ]\n          );\n          return;\n        }\n      }\n      \n      // Immediately get current location and set it - this prevents the \"not available\" error\n      try {\n        console.log(\"Getting initial location...\");\n        const initialLocation = await Location.getCurrentPositionAsync({\n          accuracy: Location.Accuracy.Balanced\n        });\n        \n        const formattedLocation = {\n          latitude: initialLocation.coords.latitude,\n          longitude: initialLocation.coords.longitude,\n        };\n        \n        console.log(\"Initial location obtained:\", JSON.stringify(formattedLocation));\n        setLocation(formattedLocation);\n        \n        // IMPORTANT: Store location in AsyncStorage so it's available immediately\n        await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(formattedLocation));\n        \n        // Update server with initial location\n        if (socketRef.current && socketRef.current.connected && user) {\n          socketRef.current.emit('updateLocation', {\n            userId: user.id,\n            location: formattedLocation\n          });\n        }\n      } catch (initLocError) {\n        console.error(\"Error getting initial location:\", initLocError);\n        // Try to recover by using last known location from storage\n        try {\n          const storedLocation = await AsyncStorage.getItem('lastKnownLocation');\n          if (storedLocation) {\n            const parsedLocation = JSON.parse(storedLocation);\n            console.log(\"Using stored location:\", JSON.stringify(parsedLocation));\n            setLocation(parsedLocation);\n          }\n        } catch (storageError) {\n          console.error(\"Error reading stored location:\", storageError);\n        }\n      }\n      \n      // Then start watching for location updates\n      console.log(\"Starting location watcher...\");\n      locationWatcher = await Location.watchPositionAsync(\n        {\n          accuracy: Location.Accuracy.High,\n          distanceInterval: 10, // Update every 10 meters\n          timeInterval: 5000,   // Or at least every 5 seconds\n        },\n        (location) => {\n          const newLocation = {\n            latitude: location.coords.latitude,\n            longitude: location.coords.longitude,\n          };\n          \n          console.log(\"Location updated:\", JSON.stringify(newLocation));\n          setLocation(newLocation);\n          \n          // IMPORTANT: Store each location update to ensure we always have the latest\n          AsyncStorage.setItem('lastKnownLocation', JSON.stringify(newLocation))\n            .catch(err => console.error(\"Error saving location:\", err));\n          \n          // Update server with new location\n          if (socketRef.current && socketRef.current.connected && user) {\n            socketRef.current.emit('updateLocation', {\n              userId: user.id,\n              location: newLocation\n            });\n          }\n        }\n      );\n      \n      console.log(\"Location watch setup complete\");\n      \n    } catch (error) {\n      console.error('Error setting up location:', error);\n      Alert.alert(\n        \"Location Error\",\n        \"There was a problem accessing your location. Please make sure location services are enabled and try again.\"\n      );\n    }\n  };\n\n  // Handle shake event with enhanced error handling\n  const handleShake = () => {\n    // Don't trigger if we're already searching or missing required data\n    if (shakeDetected || !user || !user.id) {\n      const reason = shakeDetected ? \"already shaking\" : \"missing user ID\";\n      console.log(`Shake ignored - ${reason}`);\n      return;\n    }\n    \n    console.log(\"Shake handler triggered\");\n    \n    // IMPORTANT: Always try to get the latest saved location first\n    // This prevents the \"Location not available\" error\n    const getLatestLocation = async () => {\n      try {\n        // Try to get from AsyncStorage first as it's faster\n        const storedLocation = await AsyncStorage.getItem('lastKnownLocation');\n        if (storedLocation) {\n          const parsedLocation = JSON.parse(storedLocation);\n          console.log(\"Retrieved stored location:\", JSON.stringify(parsedLocation));\n          \n          // Set location state directly to avoid \"not available\" error\n          setLocation(parsedLocation);\n          \n          // We have a location, proceed with shake using stored location\n          return processShake(parsedLocation);\n        }\n      } catch (error) {\n        console.log(\"No stored location available, trying GPS\");\n      }\n      \n      // If no stored location is available, continue with the original flow\n      if (!location) {\n        // Get fresh location from GPS\n        try {\n          console.log(\"Getting fresh location...\");\n          const currentLocation = await Location.getCurrentPositionAsync({\n            accuracy: Location.Accuracy.Balanced\n          });\n          \n          const formattedLocation = {\n            latitude: currentLocation.coords.latitude,\n            longitude: currentLocation.coords.longitude,\n          };\n          \n          console.log(\"Retrieved current location:\", JSON.stringify(formattedLocation));\n          \n          // Store for future use\n          await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(formattedLocation));\n          setLocation(formattedLocation);\n          \n          // Process shake with fresh location\n          return processShake(formattedLocation);\n        } catch (locationError) {\n          console.error(\"Error getting current location:\", locationError);\n          Alert.alert(\n            \"Location Required\",\n            \"Please make sure location services are enabled with high accuracy.\",\n            [\n              { text: \"OK\" },\n              { \n                text: \"Open Settings\", \n                onPress: () => {\n                  if (Platform.OS === 'ios') {\n                    Linking.openURL('app-settings:');\n                  } else {\n                    Linking.openSettings();\n                  }\n                }\n              }\n            ]\n          );\n          setShakeDetected(false);\n          return;\n        }\n      }\n      \n      // If we already have a location from state, use it\n      return processShake(location);\n    };\n    \n    // Start the location retrieval process\n    getLatestLocation();\n  };\n\n  // Helper function to process shake with a valid location\n  const processShake = (currentLocation) => {\n    setShakeDetected(true);\n    \n    // Log user information for debugging\n    console.log(`Shake initiated by user: ${user.username}, ID: ${user.id}`);\n    \n    // Check if socket is connected\n    if (!socketRef.current || !socketRef.current.connected) {\n      console.error(\"Socket not connected. Attempting to reconnect...\");\n      // Try to reconnect socket\n      if (serverAddress) {\n        socketRef.current = io(`http://${serverAddress}`);\n        socketRef.current.on('connect', () => {\n          console.log('Socket reconnected, registering user');\n          socketRef.current.emit('register', {\n            userId: user.id,\n            username: user.username\n          });\n        });\n      }\n      \n      Alert.alert(\n        \"Connection Issue\",\n        \"There was a problem connecting to the server. Please try again.\"\n      );\n      \n      setShakeDetected(false);\n      return;\n    }\n    \n    // Send shake event to server\n    if (isLoggedIn && socketRef.current && socketRef.current.connected) {\n      console.log(\"Sending shake event to server\");\n      socketRef.current.emit('shake', {\n        userId: user.id,\n        username: user.username,\n        location: currentLocation,\n        maxDistance,\n        timestamp: new Date().toISOString(),\n        blockedUsers,\n        rematchEnabled: true,\n        minAge: minAge,\n        maxAge: maxAge\n      });\n      \n      console.log(`Shake event sent with userID: ${user.id}, username: ${user.username}, location:`, JSON.stringify(currentLocation));\n    } else {\n      console.log(\"Cannot send shake event:\", {\n        isLoggedIn,\n        socketConnected: socketRef.current && socketRef.current.connected,\n        hasLocation: !!currentLocation\n      });\n    }\n    \n    // Reset shake detection after a delay\n    setTimeout(() => {\n      console.log(\"Resetting shake detection state\");\n      setShakeDetected(false);\n    }, 2000); // 2 second cooldown\n  };\n\n  // Handle login with improved user ID handling\n  const handleLogin = async (userData, server) => {\n    console.log('=== APP.JS HANDLE LOGIN CALLED ===');\n    console.log('userData received:', JSON.stringify(userData));\n    console.log('server received:', server);\n    \n    // Add validation to ensure userData is properly structured\n    if (!userData) {\n      console.error('Error: userData is null or undefined');\n      Alert.alert('Login Error', 'Invalid user data received. Please try again.');\n      return;\n    }\n    \n    if (typeof userData !== 'object') {\n      console.error('Error: userData is not an object, type:', typeof userData);\n      Alert.alert('Login Error', 'Received invalid user data format. Please try again.');\n      return;\n    }\n    \n    // Validate the user ID\n    const userId = userData.id;\n    if (!userId) {\n      console.error('Error: No user ID in userData:', userData);\n      Alert.alert('Login Error', 'User ID missing from login data. Please try again.');\n      return;\n    }\n    \n    console.log('User ID validation passed:', userId);\n    \n    // Create a clean user object with explicitly defined properties\n    const user = {\n      id: userId,\n      username: userData.username || 'User'\n    };\n    \n    console.log('Setting user state to:', JSON.stringify(user));\n    setUser(user);\n    \n    console.log('Setting isLoggedIn to true');\n    setIsLoggedIn(true);\n    \n    console.log('Setting serverAddress to:', server);\n    setServerAddress(server);\n    \n    try {\n      // Define exactly what we're storing\n      const storageData = JSON.stringify(user);\n      console.log('Storing in AsyncStorage:', storageData);\n      \n      // Try to store item by item with error handling\n      try {\n        await AsyncStorage.setItem('user', storageData);\n        console.log('AsyncStorage: user data stored successfully');\n      } catch (userError) {\n        console.error('AsyncStorage: Error storing user data:', userError);\n      }\n      \n      try {\n        await AsyncStorage.setItem('serverAddress', server);\n        console.log('AsyncStorage: server address stored successfully');\n      } catch (serverError) {\n        console.error('AsyncStorage: Error storing server address:', serverError);\n      }\n      \n      // Verify storage\n      try {\n        const storedUser = await AsyncStorage.getItem('user');\n        console.log('Verification - stored user data:', storedUser);\n      } catch (verifyError) {\n        console.error('Error verifying stored user data:', verifyError);\n      }\n    } catch (error) {\n      console.error('Overall error in storing login data:', error);\n    }\n    \n    console.log('=== LOGIN PROCESS COMPLETED ===');\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    if (socketRef.current) {\n      socketRef.current.disconnect();\n    }\n    \n    // Store server address temporarily\n    const currentServer = serverAddress;\n    \n    // Clear state\n    setUser(null);\n    setIsLoggedIn(false);\n    setMatches([]);\n    setBlockedUsers([]);\n    \n    try {\n      // Remove user data but keep server address\n      await AsyncStorage.removeItem('user');\n      \n      // Make sure we restore the server address\n      setServerAddress(currentServer);\n    } catch (error) {\n      console.error('Error during logout:', error);\n    }\n  };\n\n  // Update max distance setting\n  const updateMaxDistance = async (distance) => {\n    setMaxDistance(distance);\n    try {\n      await AsyncStorage.setItem('maxDistance', distance.toString());\n    } catch (error) {\n      console.error('Error saving max distance:', error);\n    }\n  };\n\n  return (\n    <View style={{ flex: 1 }}>\n      <StatusBar barStyle=\"dark-content\" backgroundColor=\"#fff\" />\n      \n      <NavigationContainer ref={navigationRef}>\n        <Stack.Navigator \n          initialRouteName={isLoggedIn ? \"Home\" : \"Login\"}\n          screenOptions={{\n            headerShown: true,\n            headerStyle: {\n              backgroundColor: '#fff',\n              elevation: 0,\n              shadowOpacity: 0,\n              borderBottomWidth: 1,\n              borderBottomColor: '#eee',\n            },\n            headerTitleStyle: {\n              fontWeight: '600',\n              color: '#4e9af1',\n            },\n            headerTintColor: '#4e9af1',\n          }}\n        >\n          {!isLoggedIn ? (\n            // Auth screens\n            <>\n              <Stack.Screen \n                name=\"Login\" \n                options={{ \n                  title: \"Welcome\",\n                  headerShown: false\n                }}\n              >\n                {props => (\n                  <LoginScreen \n                    {...props} \n                    onLogin={handleLogin} \n                    serverAddress={serverAddress} \n                  />\n                )}\n              </Stack.Screen>\n              <Stack.Screen \n                name=\"Register\"\n                options={{ \n                  title: \"Create Account\",\n                  headerShown: false\n                }}\n              >\n                {props => (\n                  <RegisterScreen \n                    {...props} \n                    serverAddress={serverAddress} \n                  />\n                )}\n              </Stack.Screen>\n            </>\n          ) : (\n            <>\n              <Stack.Screen \n                name=\"Home\"\n                options={{ title: \"Shake & Match\" }}\n              >\n                {props => (\n                  <HomeScreen \n                    {...props} \n                    user={user} \n                    shakeDetected={shakeDetected} \n                    matches={matches}\n                    onShake={handleShake}\n                    onDeleteMatch={handleDeleteChat}\n                    onBlockUser={handleBlockUser}\n                    serverAddress={serverAddress}\n                  />\n                )}\n              </Stack.Screen>\n              \n              {/* All other screens remain the same */}\n              <Stack.Screen name=\"Settings\">\n                {props => <SettingsScreen {...props} maxDistance={maxDistance} minAge={minAge} maxAge={maxAge} blockedUsers={blockedUsers} onUpdateMaxDistance={updateMaxDistance} onUpdateAgeRange={updateAgeRange} onUnblockUser={handleUnblockUser} onLogout={handleLogout} />}\n              </Stack.Screen>\n              <Stack.Screen name=\"Chat\">\n                {props => <ChatScreen {...props} socket={socketRef.current} user={user} onDeleteChat={handleDeleteChat} onBlockUser={handleBlockUser} />}\n              </Stack.Screen>\n              <Stack.Screen name=\"Match\">\n                {props => <MatchScreen {...props} serverAddress={serverAddress} />}\n              </Stack.Screen>\n              <Stack.Screen name=\"ViewProfile\" component={ViewProfileScreen} />\n              <Stack.Screen name=\"ProfileScreen\">\n  {props => (\n    <ProfileScreen \n      {...props} \n      user={user} \n      serverAddress={serverAddress} \n      onUpdateProfile={handleUpdateProfile}\n      isInitialSetup={props.route.params?.isInitialSetup || false}\n    />\n  )}\n</Stack.Screen>\n            </>\n          )}\n        </Stack.Navigator>\n      </NavigationContainer>\n    </View>\n  );\n}"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,mBAAmB,KAAQ,0BAA0B,CAC9D,OAASC,oBAAoB,KAAQ,yBAAyB,CAC9D,OAASC,aAAa,KAAQ,cAAc,CAC5C,MAAO,GAAK,CAAAC,QAAQ,KAAM,eAAe,CACzC,OAASC,EAAE,KAAQ,kBAAkB,CACrC,MAAO,CAAAC,YAAY,KAAM,2CAA2C,CAAC,OAAAC,SAAA,sDAAAC,IAAA,iDAAAC,KAAA,kDAAAC,OAAA,oDAAAC,QAAA,8CAErE,MAAO,8BAA8B,CAGrC,MAAO,CAAAC,WAAW,KAAM,uBAAuB,CAC/C,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,MAAO,CAAAC,WAAW,KAAM,uBAAuB,CAC/C,MAAO,CAAAC,iBAAiB,KAAM,6BAA6B,CAC3D,MAAO,CAAAC,aAAa,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,KAAK,CAAGxB,oBAAoB,CAAC,CAAC,CAEpC,cAAe,SAAS,CAAAyB,GAAGA,CAAA,CAAG,CAE5B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgC,IAAI,CAAEC,OAAO,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGrC,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACsC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACwC,OAAO,CAAEC,UAAU,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0C,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAA4C,SAAS,CAAG1C,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAC2C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAA+C,aAAa,CAAG7C,MAAM,CAAC,IAAI,CAAC,CAClC,KAAM,CAAA8C,0BAA0B,CAAG9C,MAAM,CAAC,IAAI,CAAC,CAC/C,KAAM,CAAC+C,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACmD,MAAM,CAAEC,SAAS,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACqD,MAAM,CAAEC,SAAS,CAAC,CAAGtD,QAAQ,CAAC,GAAG,CAAC,CAGzCC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsD,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAhD,YAAY,CAACiD,OAAO,CAAC,MAAM,CAAC,CACnD,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAlD,YAAY,CAACiD,OAAO,CAAC,eAAe,CAAC,CAChE,KAAM,CAAAE,iBAAiB,CAAG,KAAM,CAAAnD,YAAY,CAACiD,OAAO,CAAC,aAAa,CAAC,CACnE,KAAM,CAAAG,YAAY,CAAG,KAAM,CAAApD,YAAY,CAACiD,OAAO,CAAC,QAAQ,CAAC,CACzD,KAAM,CAAAI,YAAY,CAAG,KAAM,CAAArD,YAAY,CAACiD,OAAO,CAAC,QAAQ,CAAC,CAEzD,GAAI,CAAAK,eAAe,CAAG,KAAK,CAE3B,GAAIN,QAAQ,EAAIE,YAAY,CAAE,CAC5BK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAER,QAAQ,CAAC,CAEhD,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACX,QAAQ,CAAC,CAGrC,GAAI,CAACS,QAAQ,CAACG,EAAE,CAAE,CAChBL,OAAO,CAACM,KAAK,CAAC,8BAA8B,CAAEJ,QAAQ,CAAC,CAEvD,KAAM,CAAAzD,YAAY,CAAC8D,UAAU,CAAC,MAAM,CAAC,CACvC,CAAC,IAAM,CAEL,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,UAAUd,YAAY,mBAAmBO,QAAQ,CAACG,EAAE,EAAE,CAAE,CACnFK,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElC,GAAI,CAACL,QAAQ,CAACM,EAAE,EAAI,CAACF,IAAI,CAACG,MAAM,CAAE,CAEhCf,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACtE,KAAM,CAAAxD,YAAY,CAAC8D,UAAU,CAAC,MAAM,CAAC,CACvC,CAAC,IAAM,CAELP,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzCD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEC,QAAQ,CAACc,QAAQ,CAAEd,QAAQ,CAACG,EAAE,CAAC,CAClFnC,OAAO,CAACgC,QAAQ,CAAC,CACjBlC,aAAa,CAAC,IAAI,CAAC,CACnBe,gBAAgB,CAACY,YAAY,CAAC,CAC9BI,eAAe,CAAG,IAAI,CACxB,CACF,CAAE,MAAOkB,eAAe,CAAE,CACxBjB,OAAO,CAACM,KAAK,CAAC,wBAAwB,CAAEW,eAAe,CAAC,CAExD,KAAM,CAAAxE,YAAY,CAAC8D,UAAU,CAAC,MAAM,CAAC,CACvC,CACF,CACF,CAAE,MAAOW,UAAU,CAAE,CACnBlB,OAAO,CAACM,KAAK,CAAC,iCAAiC,CAAEY,UAAU,CAAC,CAC5D,KAAM,CAAAzE,YAAY,CAAC8D,UAAU,CAAC,MAAM,CAAC,CACvC,CACF,CAAC,IAAM,CACLP,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CACpE,CAEA,GAAIN,YAAY,CAAE,CAChBZ,gBAAgB,CAACY,YAAY,CAAC,CAChC,CAEA,GAAIC,iBAAiB,CAAE,CACrBpB,cAAc,CAAC2C,UAAU,CAACvB,iBAAiB,CAAC,EAAI,CAAC,CAAC,CACpD,CAEA,GAAIC,YAAY,CAAE,CAChBR,SAAS,CAAC+B,QAAQ,CAACvB,YAAY,CAAC,EAAI,EAAE,CAAC,CACzC,CAEA,GAAIC,YAAY,CAAE,CAChBP,SAAS,CAAC6B,QAAQ,CAACtB,YAAY,CAAC,EAAI,GAAG,CAAC,CAC1C,CAGA,GAAI,CAACC,eAAe,CAAE,CACpB/B,aAAa,CAAC,KAAK,CAAC,CACpBE,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAE,MAAOoC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDtC,aAAa,CAAC,KAAK,CAAC,CACpBE,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAC,CAEDsB,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAGN,KAAM,CAAA6B,cAAc,CAAG,KAAAA,CAAOC,GAAG,CAAEC,GAAG,GAAK,CACzClC,SAAS,CAACiC,GAAG,CAAC,CACd/B,SAAS,CAACgC,GAAG,CAAC,CACd,GAAI,CACF,KAAM,CAAA9E,YAAY,CAAC+E,OAAO,CAAC,QAAQ,CAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAC,CACpD,KAAM,CAAAhF,YAAY,CAAC+E,OAAO,CAAC,QAAQ,CAAED,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC,CACtD,CAAE,MAAOnB,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CACF,CAAC,CAGDpE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwF,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,GAAIzD,IAAI,EAAEoC,EAAE,CAAE,CAEZ,KAAM,CAAAsB,aAAa,CAAG,KAAM,CAAAlF,YAAY,CAACiD,OAAO,CAAC,WAAWzB,IAAI,CAACoC,EAAE,EAAE,CAAC,CACtE,GAAIsB,aAAa,CAAE,CACjBjD,UAAU,CAACyB,IAAI,CAACC,KAAK,CAACuB,aAAa,CAAC,CAAC,CACvC,CAGA,KAAM,CAAAC,kBAAkB,CAAG,KAAM,CAAAnF,YAAY,CAACiD,OAAO,CAAC,WAAWzB,IAAI,CAACoC,EAAE,EAAE,CAAC,CAC3E,GAAIuB,kBAAkB,CAAE,CACtBhD,eAAe,CAACuB,IAAI,CAACC,KAAK,CAACwB,kBAAkB,CAAC,CAAC,CACjD,CAEA,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAApF,YAAY,CAACiD,OAAO,CAAC,WAAWzB,IAAI,CAACoC,EAAE,EAAE,CAAC,CACtE,GAAIwB,aAAa,CAAE,CACjB1C,cAAc,CAACgB,IAAI,CAACC,KAAK,CAACyB,aAAa,CAAC,CAAC,CAC3C,CACF,CACF,CAAE,MAAOvB,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CAAC,CAED,GAAIvC,UAAU,EAAIE,IAAI,CAAE,CACtByD,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAAC3D,UAAU,CAAEE,IAAI,CAAC,CAAC,CAEtB/B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4F,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI/D,UAAU,EAAIE,IAAI,EAAEoC,EAAE,CAAE,CAC1B,GAAI,CAEF,KAAM,CAAAwB,aAAa,CAAG,KAAM,CAAApF,YAAY,CAACiD,OAAO,CAAC,WAAWzB,IAAI,CAACoC,EAAE,EAAE,CAAC,CACtE,GAAI,CAAA0B,WAAW,CAAG,IAAI,CAEtB,GAAIF,aAAa,CAAE,CACjBE,WAAW,CAAG5B,IAAI,CAACC,KAAK,CAACyB,aAAa,CAAC,CACzC,CAGA,KAAM,CAAAG,iBAAiB,CACrBD,WAAW,EACXA,WAAW,CAACE,WAAW,EACvBF,WAAW,CAACG,GAAG,EACfH,WAAW,CAACI,MAAM,EAClBJ,WAAW,CAACI,MAAM,CAACC,MAAM,EAAI,CAAC,CAGhC,GAAI,CAACJ,iBAAiB,EAAIhD,aAAa,CAACqD,OAAO,CAAE,CAC/CrC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAE/DqC,UAAU,CAAC,IAAM,CACftD,aAAa,CAACqD,OAAO,CAACE,KAAK,CAAC,CAC1BC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACPC,IAAI,CAAE,eAAe,CACrBC,MAAM,CAAE,CAAEC,cAAc,CAAE,IAAK,CACjC,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAAE,MAAOtC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC5D,CACF,CACF,CAAC,CAEDwB,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAAE,CAAC/D,UAAU,CAAEE,IAAI,CAAC,CAAC,CAEtB,KAAM,CAAA4E,mBAAmB,CAAId,WAAW,EAAK,CAC3C5C,cAAc,CAAC4C,WAAW,CAAC,CAC7B,CAAC,CAGD,KAAM,CAAAe,aAAa,CAAIC,MAAM,EAAK,CAChC,MAAO,CAAApE,YAAY,CAACqE,QAAQ,CAACD,MAAM,CAAC,CACtC,CAAC,CAGD,KAAM,CAAAE,gBAAgB,CAAIF,MAAM,EAAK,CACnC,GAAI,CAAC/D,aAAa,CAACqD,OAAO,CAAE,MAAO,MAAK,CAExC,GAAI,CACF,KAAM,CAAAa,YAAY,CAAGlE,aAAa,CAACqD,OAAO,CAACc,eAAe,CAAC,CAAC,CAC5D,GAAID,YAAY,EAAER,IAAI,GAAK,MAAM,EAAIQ,YAAY,EAAEP,MAAM,EAAES,KAAK,EAAEL,MAAM,GAAKA,MAAM,CAAE,CACnF,MAAO,KAAI,CACb,CACF,CAAE,MAAOM,CAAC,CAAE,CAEVrD,OAAO,CAACM,KAAK,CAAC,+BAA+B,CAAE+C,CAAC,CAAC,CACnD,CAEA,MAAO,MAAK,CACd,CAAC,CAGD,KAAM,CAAAC,eAAe,CAAG,KAAO,CAAAP,MAAM,EAAK,CACxC,GAAI,CACF,GAAI,CAAC9E,IAAI,EAAEoC,EAAE,EAAI,CAAC0C,MAAM,CAAE,CACxB/C,OAAO,CAACM,KAAK,CAAC,iDAAiD,CAAC,CAChE,OACF,CAGA,GAAIwC,aAAa,CAACC,MAAM,CAAC,CAAE,CACzB,OACF,CAGA,KAAM,CAAAQ,mBAAmB,CAAG,CAAC,GAAG5E,YAAY,CAAEoE,MAAM,CAAC,CACrDnE,eAAe,CAAC2E,mBAAmB,CAAC,CAGpC,KAAM,CAAA9G,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAACD,mBAAmB,CAAC,CAAC,CAGrF,KAAM,CAAAE,gBAAgB,CAACV,MAAM,CAAE,IAAI,CAAC,CAGpC,GAAIlE,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CACpD7E,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,WAAW,CAAE,CAClCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfuD,aAAa,CAAEb,MACjB,CAAC,CAAC,CACJ,CAEA/C,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAC1C,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,CAGD,KAAM,CAAAuD,iBAAiB,CAAG,KAAO,CAAAd,MAAM,EAAK,CAC1C,GAAI,CACF,GAAI,CAAC9E,IAAI,EAAEoC,EAAE,EAAI,CAAC0C,MAAM,CAAE,CACxB/C,OAAO,CAACM,KAAK,CAAC,mDAAmD,CAAC,CAClE,OACF,CAGA,KAAM,CAAAiD,mBAAmB,CAAG5E,YAAY,CAACmF,MAAM,CAACzD,EAAE,EAAIA,EAAE,GAAK0C,MAAM,CAAC,CACpEnE,eAAe,CAAC2E,mBAAmB,CAAC,CAGpC,KAAM,CAAA9G,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAACD,mBAAmB,CAAC,CAAC,CAGrF,GAAI1E,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CACpD7E,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,aAAa,CAAE,CACpCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACf0D,eAAe,CAAEhB,MACnB,CAAC,CAAC,CACJ,CAEA/C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC5C,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAGD,KAAM,CAAA0D,oBAAoB,CAAG,KAAO,CAAAC,WAAW,EAAK,CAClD,GAAI,CACF,GAAI,CAACA,WAAW,EAAI,CAACA,WAAW,CAACC,QAAQ,EAAI,CAACD,WAAW,CAACE,UAAU,CAAE,CACpEnE,OAAO,CAACM,KAAK,CAAC,kCAAkC,CAAC,CACjD,OACF,CAGA,KAAM,CAAA8D,MAAM,CAAG,CAACH,WAAW,CAACC,QAAQ,CAAED,WAAW,CAACE,UAAU,CAAC,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAG9E,KAAM,CAAAC,kBAAkB,CAAG,KAAM,CAAA9H,YAAY,CAACiD,OAAO,CAAC,QAAQ0E,MAAM,EAAE,CAAC,CACvE,GAAI,CAAAI,cAAc,CAAG,EAAE,CAEvB,GAAID,kBAAkB,CAAE,CACtBC,cAAc,CAAGrE,IAAI,CAACC,KAAK,CAACmE,kBAAkB,CAAC,CACjD,CAGA,KAAM,CAAAE,gBAAgB,CAAG,CACvBpE,EAAE,CAAE4D,WAAW,CAAC5D,EAAE,EAAIqE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAClD,QAAQ,CAAC,CAAC,CAC3CmD,IAAI,CAAEX,WAAW,CAACW,IAAI,CACtBV,QAAQ,CAAED,WAAW,CAACC,QAAQ,CAC9BW,SAAS,CAAEZ,WAAW,CAACY,SAAS,EAAI,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAC7D,CAAC,CAGD,KAAM,CAAAC,aAAa,CAAGP,cAAc,CAACQ,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC5E,EAAE,GAAKoE,gBAAgB,CAACpE,EAAE,CAAC,CAChF,GAAI0E,aAAa,CAAE,CACjB,OACF,CAGA,KAAM,CAAAG,eAAe,CAAG,CAAC,GAAGV,cAAc,CAAEC,gBAAgB,CAAC,CAG7D,KAAM,CAAAhI,YAAY,CAAC+E,OAAO,CAAC,QAAQ4C,MAAM,EAAE,CAAEjE,IAAI,CAACqD,SAAS,CAAC0B,eAAe,CAAC,CAAC,CAG7EC,uBAAuB,CAAClB,WAAW,CAACC,QAAQ,GAAKjG,IAAI,CAACoC,EAAE,CAAG4D,WAAW,CAACE,UAAU,CAAGF,WAAW,CAACC,QAAQ,CAAC,CAEzGlE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACtD,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CACF,CAAC,CAGD,KAAM,CAAA6E,uBAAuB,CAAIC,WAAW,EAAK,CAC/C1G,UAAU,CAAC2G,WAAW,EAAI,CACxB,KAAM,CAAAC,cAAc,CAAGD,WAAW,CAACE,GAAG,CAACnC,KAAK,EAAI,CAC9C,GAAIA,KAAK,CAACL,MAAM,GAAKqC,WAAW,CAAE,CAChC,OAAAI,MAAA,CAAAC,MAAA,IACKrC,KAAK,EACRsC,YAAY,CAAE,GAAI,CAAAhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACtCa,WAAW,CAAE,IAAI,GAErB,CACA,MAAO,CAAAvC,KAAK,CACd,CAAC,CAAC,CAGF,GAAI,CACF,GAAInF,IAAI,EAAEoC,EAAE,CAAE,CACZ5D,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAC5E,CACF,CAAE,MAAOjC,CAAC,CAAE,CACVrD,OAAO,CAACM,KAAK,CAAC,+BAA+B,CAAE+C,CAAC,CAAC,CACnD,CAEA,MAAO,CAAAiC,cAAc,CACvB,CAAC,CAAC,CACJ,CAAC,CAGD,KAAM,CAAA7B,gBAAgB,CAAG,KAAAA,CAAO2B,WAAW,CAAEQ,UAAU,CAAG,KAAK,GAAK,CAClE,GAAI,CACF,GAAI,CAAC3H,IAAI,EAAEoC,EAAE,EAAI,CAAC+E,WAAW,CAAE,CAC7BpF,OAAO,CAACM,KAAK,CAAC,iDAAiD,CAAC,CAChE,OACF,CAGA,KAAM,CAAA8D,MAAM,CAAG,CAACnG,IAAI,CAACoC,EAAE,CAAE+E,WAAW,CAAC,CAACf,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACtD,KAAM,CAAA7H,YAAY,CAAC8D,UAAU,CAAC,QAAQ6D,MAAM,EAAE,CAAC,CAG/C,KAAM,CAAAkB,cAAc,CAAG7G,OAAO,CAACqF,MAAM,CAACV,KAAK,EAAIA,KAAK,CAACL,MAAM,GAAKqC,WAAW,CAAC,CAC5E1G,UAAU,CAAC4G,cAAc,CAAC,CAG1B,KAAM,CAAA7I,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAGhF,GAAIzG,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CAEpD,GAAI,CAACkC,UAAU,CAAE,CACf/G,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,cAAc,CAAE,CACrCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfwF,WAAW,CAAET,WACf,CAAC,CAAC,CACJ,CAGAvG,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,aAAa,CAAE,CACpCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfwF,WAAW,CAAET,WAAW,CACxBU,MAAM,CAAEF,UAAU,CAAG,SAAS,CAAG,SAAS,CAC1CG,YAAY,CAAE,CAACH,UACjB,CAAC,CAAC,CACJ,CAEA5F,OAAO,CAACC,GAAG,CAAC,sCAAsC2F,UAAU,CAAG,aAAa,CAAG,SAAS,EAAE,CAAC,CAC7F,CAAE,MAAOtF,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,CAGDpE,SAAS,CAAC,IAAM,CACd,GAAI6B,UAAU,EAAIE,IAAI,CAAE,CAEtB,GAAIgB,0BAA0B,CAACoD,OAAO,CAAE,CACtC2D,aAAa,CAAC/G,0BAA0B,CAACoD,OAAO,CAAC,CACnD,CAGApD,0BAA0B,CAACoD,OAAO,CAAG4D,WAAW,CAAC,SAAY,CAC3D,GAAI,CAEFvH,UAAU,CAAC2G,WAAW,EAAI,CACxB,KAAM,CAAAV,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAY,cAAc,CAAGD,WAAW,CAACvB,MAAM,CAACV,KAAK,EAAI,CAEjD,GAAIA,KAAK,CAACuC,WAAW,CAAE,MAAO,KAAI,CAGlC,KAAM,CAAAO,cAAc,CAAG,GAAI,CAAAxB,IAAI,CAACtB,KAAK,CAAC+C,SAAS,EAAIxB,GAAG,CAAC,CACvD,KAAM,CAAAyB,iBAAiB,CAAG,EAAE,CAAG,IAAI,CACnC,KAAM,CAAAC,UAAU,CAAI1B,GAAG,CAAGuB,cAAc,CAAIE,iBAAiB,CAG7D,GAAIC,UAAU,CAAE,CAEd,KAAM,CAAAjC,MAAM,CAAG,CAACnG,IAAI,CAACoC,EAAE,CAAE+C,KAAK,CAACL,MAAM,CAAC,CAACsB,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACvD7H,YAAY,CAAC8D,UAAU,CAAC,QAAQ6D,MAAM,EAAE,CAAC,CAGzC,GAAIvF,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CAEpD7E,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,cAAc,CAAE,CACrCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfwF,WAAW,CAAEzC,KAAK,CAACL,MACrB,CAAC,CAAC,CAGFlE,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,aAAa,CAAE,CACpCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfwF,WAAW,CAAEzC,KAAK,CAACL,MAAM,CACzB+C,MAAM,CAAE,UAAU,CAClBC,YAAY,CAAE,IAChB,CAAC,CAAC,CACJ,CAEA,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAC,CAAC,CAGF,GAAIT,cAAc,CAAClD,MAAM,GAAKiD,WAAW,CAACjD,MAAM,CAAE,CAChD3F,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAC5E,CAEA,MAAO,CAAAA,cAAc,CACvB,CAAC,CAAC,CACJ,CAAE,MAAOhF,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CACF,CAAC,CAAE,KAAK,CAAC,CAGT,MAAO,IAAM,CACX,GAAIrB,0BAA0B,CAACoD,OAAO,CAAE,CACtC2D,aAAa,CAAC/G,0BAA0B,CAACoD,OAAO,CAAC,CACnD,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACtE,UAAU,CAAEE,IAAI,CAAC,CAAC,CAGtB/B,SAAS,CAAC,IAAM,CACd,GAAI6B,UAAU,EAAIE,IAAI,EAAIa,aAAa,CAAE,CAEvCD,SAAS,CAACwD,OAAO,CAAG7F,EAAE,CAAC,UAAUsC,aAAa,EAAE,CAAC,CAEjDD,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,SAAS,CAAE,IAAM,CACpCtG,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAElCpB,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,UAAU,CAAE,CACjCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfW,QAAQ,CAAE/C,IAAI,CAAC+C,QACjB,CAAC,CAAC,CAGFnC,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,aAAa,CAAE,CACpCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACf1B,YAAY,CAAEA,YAChB,CAAC,CAAC,CAGFE,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,wBAAwB,CAAE,CAC/CZ,MAAM,CAAE9E,IAAI,CAACoC,EACf,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFxB,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,OAAO,CAAGC,SAAS,EAAK,CAC3CvG,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEsG,SAAS,CAAC,CAG7C,GAAI,CAACA,SAAS,EAAI,CAACA,SAAS,CAACxD,MAAM,CAAE,CACnC/C,OAAO,CAACM,KAAK,CAAC,8BAA8B,CAAEiG,SAAS,CAAC,CACxD,OACF,CAGA,GAAIzD,aAAa,CAACyD,SAAS,CAACxD,MAAM,CAAC,CAAE,CACnC/C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEsG,SAAS,CAACxD,MAAM,CAAC,CAClE,OACF,CAEArE,UAAU,CAAC8H,IAAI,EAAI,CAEjB,KAAM,CAAAC,WAAW,CAAGD,IAAI,CAACxB,IAAI,CAAC0B,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKwD,SAAS,CAACxD,MAAM,CAAC,CACjE,GAAI0D,WAAW,CAAE,CACf,MAAO,CAAAD,IAAI,CACb,CAGA,KAAM,CAAAG,YAAY,CAAAnB,MAAA,CAAAC,MAAA,IACbc,SAAS,EACZJ,SAAS,CAAE,GAAI,CAAAzB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCa,WAAW,CAAE,KAAK,EACnB,CAED,KAAM,CAAAL,cAAc,CAAG,CAAC,GAAGkB,IAAI,CAAEG,YAAY,CAAC,CAG9C,GAAI,CACFlK,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAC5E,CAAE,MAAOjC,CAAC,CAAE,CACVrD,OAAO,CAACM,KAAK,CAAC,uBAAuB,CAAE+C,CAAC,CAAC,CAC3C,CAEA,MAAO,CAAAiC,cAAc,CACvB,CAAC,CAAC,CACJ,CAAC,CAAC,CAGRzG,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,UAAU,CAAE,CAAC,CAAEM,MAAM,CAAEjE,MAAO,CAAC,GAAK,CACvD3C,OAAO,CAACC,GAAG,CAAC,2BAA2B2G,MAAM,GAAG,CAAEjE,MAAM,CAAC,CAGzD,GAAIiE,MAAM,GAAK,OAAO,CAAE,CACtB5G,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChD,OACF,CAEA,GAAIjB,aAAa,CAACqD,OAAO,EAAIuE,MAAM,CAAE,CACnC,GAAI,CAEFtE,UAAU,CAAC,IAAM,CACftD,aAAa,CAACqD,OAAO,CAACwE,QAAQ,CAACD,MAAM,CAAEjE,MAAM,CAAC,CAChD,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAOrC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CAC3C,CACF,CAAC,IAAM,CACLN,OAAO,CAACM,KAAK,CAAC,wDAAwD,CAAC,CACzE,CACF,CAAC,CAAC,CAEIzB,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,SAAS,CAAGrC,WAAW,EAAK,CAC/CjE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEgE,WAAW,CAAC,CAGjD,GAAI,CAACA,WAAW,EAAI,CAACA,WAAW,CAACC,QAAQ,EAAI,CAACD,WAAW,CAACE,UAAU,CAAE,CACpEnE,OAAO,CAACM,KAAK,CAAC,gCAAgC,CAAE2D,WAAW,CAAC,CAC5D,OACF,CAGA,GAAInB,aAAa,CAACmB,WAAW,CAACC,QAAQ,CAAC,CAAE,CACvClE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEgE,WAAW,CAACC,QAAQ,CAAC,CACxE,OACF,CAGAF,oBAAoB,CAACC,WAAW,CAAC,CAIjC,GAAIA,WAAW,CAACE,UAAU,GAAKlG,IAAI,CAACoC,EAAE,EAAI4D,WAAW,CAACC,QAAQ,GAAKjG,IAAI,CAACoC,EAAE,CAAE,CAC1E3B,UAAU,CAAC2G,WAAW,EAAI,CAExB,KAAM,CAAAyB,UAAU,CAAGzB,WAAW,CAAC0B,SAAS,CAACL,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKkB,WAAW,CAACC,QAAQ,CAAC,CAEhF,GAAI,CAAAoB,cAAc,CAAG,CAAC,GAAGD,WAAW,CAAC,CAErC,GAAIyB,UAAU,GAAK,CAAC,CAAC,CAAE,CAGrB9G,OAAO,CAACC,GAAG,CAAC,8BAA8BgE,WAAW,CAAC+C,cAAc,4BAA4B,CAAC,CAGjG,KAAM,CAAAC,QAAQ,CAAG,CACflE,MAAM,CAAEkB,WAAW,CAACC,QAAQ,CAC5BlD,QAAQ,CAAEiD,WAAW,CAAC+C,cAAc,CACpCb,SAAS,CAAE,GAAI,CAAAzB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCa,WAAW,CAAE,IAAI,CACjBD,YAAY,CAAE,GAAI,CAAAhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACtCoC,iBAAiB,CAAE,IACrB,CAAC,CAGD5B,cAAc,CAAG,CAAC,GAAGD,WAAW,CAAE4B,QAAQ,CAAC,CAC7C,CAAC,IAAM,CAEL,KAAM,CAAAE,kBAAkB,CAAGlE,gBAAgB,CAACgB,WAAW,CAACC,QAAQ,CAAC,CAGjE,KAAM,CAAAyC,YAAY,CAAAnB,MAAA,CAAAC,MAAA,IACbJ,WAAW,CAACyB,UAAU,CAAC,EAC1BnB,WAAW,CAAE,IAAI,CACjBD,YAAY,CAAE,GAAI,CAAAhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EACvC,CAGD,GAAI,CAACqC,kBAAkB,CAAE,CACvBR,YAAY,CAACO,iBAAiB,CAAG,IAAI,CACvC,CAEA5B,cAAc,CAACwB,UAAU,CAAC,CAAGH,YAAY,CAC3C,CAGA,GAAI,CACFlK,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAC5E,CAAE,MAAOjC,CAAC,CAAE,CACVrD,OAAO,CAACM,KAAK,CAAC,+BAA+B,CAAE+C,CAAC,CAAC,CACnD,CAEA,MAAO,CAAAiC,cAAc,CACvB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAGFzG,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,cAAc,CAAG1F,IAAI,EAAK,CAC7C,GAAIA,IAAI,CAACmC,MAAM,GAAK9E,IAAI,CAACoC,EAAE,CAAE,CAC3B3B,UAAU,CAAC2G,WAAW,EAAI,CACxB,KAAM,CAAAyB,UAAU,CAAGzB,WAAW,CAAC0B,SAAS,CAACL,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKnC,IAAI,CAACiF,WAAW,CAAC,CAC5E,GAAIiB,UAAU,GAAK,CAAC,CAAC,CAAE,MAAO,CAAAzB,WAAW,CAEzC,KAAM,CAAAC,cAAc,CAAG,CAAC,GAAGD,WAAW,CAAC,CACvCC,cAAc,CAACwB,UAAU,CAAC,CAAAtB,MAAA,CAAAC,MAAA,IACrBH,cAAc,CAACwB,UAAU,CAAC,EAC7BI,iBAAiB,CAAE,KAAK,CACxBvB,WAAW,CAAE,IAAI,CACjBD,YAAY,CAAE,GAAI,CAAAhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EACvC,CAGD,GAAI,CACFrI,YAAY,CAAC+E,OAAO,CAAC,WAAWvD,IAAI,CAACoC,EAAE,EAAE,CAAEF,IAAI,CAACqD,SAAS,CAAC8B,cAAc,CAAC,CAAC,CAC5E,CAAE,MAAOjC,CAAC,CAAE,CACVrD,OAAO,CAACM,KAAK,CAAC,+BAA+B,CAAE+C,CAAC,CAAC,CACnD,CAEA,MAAO,CAAAiC,cAAc,CACvB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFzG,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,gBAAgB,CAAG1F,IAAI,EAAK,CAC/C,GAAIA,IAAI,CAACgD,aAAa,GAAK3F,IAAI,CAACoC,EAAE,CAAE,CAElC,KAAM,CAAA+G,YAAY,CAAG3I,OAAO,CAAC4I,IAAI,CAACX,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKnC,IAAI,CAACmC,MAAM,CAAC,CAChE,GAAIqE,YAAY,CAAE,CAChB,KAAM,CAAAE,eAAe,CAAGF,YAAY,CAACpG,QAAQ,EAAI,SAAS,CAG1DyC,gBAAgB,CAAC7C,IAAI,CAACmC,MAAM,CAAE,KAAK,CAAC,CAGpC,GAAI,CAACE,gBAAgB,CAACrC,IAAI,CAACmC,MAAM,CAAC,CAAE,CAClCnG,KAAK,CAAC2K,KAAK,CACT,SAAS,CACT,GAAGD,eAAe,mBAAmB,CACrC,CAAC,CAAE1C,IAAI,CAAE,IAAK,CAAC,CACjB,CAAC,CACH,CACF,CACF,CACF,CAAC,CAAC,CAGF/F,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,YAAY,CAAE,IAAM,CACvCtG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAC,CAAC,CAGFpB,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,OAAO,CAAGhG,KAAK,EAAK,CACvCN,OAAO,CAACM,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,CAAC,CAEF,MAAO,IAAM,CACX,GAAIzB,SAAS,CAACwD,OAAO,CAAE,CACrBxD,SAAS,CAACwD,OAAO,CAACmF,UAAU,CAAC,CAAC,CAChC,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACzJ,UAAU,CAAEE,IAAI,CAAEa,aAAa,CAAEH,YAAY,CAAC,CAAC,CAGnDzC,SAAS,CAAC,IAAM,CACd,GAAI,CAAAuL,YAAY,CAChB,GAAI,CAAAC,aAAa,CAAG,CAAC,CACrB,GAAI,CAAAC,UAAU,CAAG,CAAC,CAElB,KAAM,CAAAC,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAvL,aAAa,CAACwL,gBAAgB,CAAC,CAAC,CAE1D,GAAI,CAACD,WAAW,CAAE,CAChB7H,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CACzD,OACF,CAGA3D,aAAa,CAACyL,iBAAiB,CAAC,GAAG,CAAC,CAEpCN,YAAY,CAAGnL,aAAa,CAAC0L,WAAW,CAACC,iBAAiB,EAAI,CAC5D,KAAM,CAAEC,CAAC,CAAEC,CAAC,CAAEC,CAAE,CAAC,CAAGH,iBAAiB,CACrC,KAAM,CAAAI,YAAY,CAAGC,IAAI,CAACC,IAAI,CAACL,CAAC,CAACA,CAAC,CAAGC,CAAC,CAACA,CAAC,CAAGC,CAAC,CAACA,CAAC,CAAC,CAC/C,KAAM,CAAAzD,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAGtB,GAAI0D,YAAY,CAAG,GAAG,CAAE,CACtBV,UAAU,EAAE,CAGZ,GAAIA,UAAU,EAAI,CAAC,EAAKhD,GAAG,CAAG+C,aAAa,CAAI,IAAI,CAAE,CACnD1H,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEoI,YAAY,CAAC,CAErDV,UAAU,CAAG,CAAC,CACdD,aAAa,CAAG/C,GAAG,CAGnB6D,WAAW,CAAC,CAAC,CACf,CAAC,IAAM,IAAK7D,GAAG,CAAG+C,aAAa,CAAI,IAAI,CAAE,CAEvCC,UAAU,CAAG,CAAC,CACdD,aAAa,CAAG/C,GAAG,CACrB,CACF,CACF,CAAC,CAAC,CAEF3E,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CAC3D,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAAC,CAED,GAAIvC,UAAU,CAAE,CACdiC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3C2H,kBAAkB,CAAC,CAAC,CACtB,CAEA,MAAO,IAAM,CACX,GAAIH,YAAY,CAAE,CAChBzH,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAC9CwH,YAAY,CAACgB,MAAM,CAAC,CAAC,CACvB,CACF,CAAC,CACH,CAAC,CAAE,CAAC1K,UAAU,CAAC,CAAC,CAGhB,KAAM,CAAA2K,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF1I,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAGtC,KAAM,CAAA0I,cAAc,CAAG,KAAM,CAAApM,QAAQ,CAACqM,sBAAsB,CAAC,CAAC,CAC9D,GAAI,CAACD,cAAc,CAACE,uBAAuB,CAAE,CAC3C7I,OAAO,CAACM,KAAK,CAAC,gDAAgD,CAAC,CAC/D1D,KAAK,CAAC2K,KAAK,CACT,4BAA4B,CAC5B,8FAA8F,CAC9F,CACE,CAAE3C,IAAI,CAAE,IAAK,CAAC,CACd,CACEA,IAAI,CAAE,eAAe,CACrBkE,OAAO,CAAEA,CAAA,GAAM,CACb,SAA2B,CACzBjM,OAAO,CAACkM,OAAO,CAAC,eAAe,CAAC,CAClC,CAAC,IAAM,CACLlM,OAAO,CAACmM,YAAY,CAAC,CAAC,CACxB,CACF,CACF,CAAC,CAEL,CAAC,CACD,OACF,CAGA,KAAM,CAAEC,MAAO,CAAC,CAAG,KAAM,CAAA1M,QAAQ,CAAC2M,6BAA6B,CAAC,CAAC,CAEjE,GAAID,MAAM,GAAK,SAAS,CAAE,CACxBjJ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAC7D,KAAM,CAAEgJ,MAAM,CAAEE,SAAU,CAAC,CAAG,KAAM,CAAA5M,QAAQ,CAAC6M,iCAAiC,CAAC,CAAC,CAEhF,GAAID,SAAS,GAAK,SAAS,CAAE,CAC3BnJ,OAAO,CAACM,KAAK,CAAC,oCAAoC,CAAC,CACnD1D,KAAK,CAAC2K,KAAK,CACT,mBAAmB,CACnB,oHAAoH,CACpH,CACE,CAAE3C,IAAI,CAAE,IAAK,CAAC,CACd,CACEA,IAAI,CAAE,eAAe,CACrBkE,OAAO,CAAEA,CAAA,GAAM,CACb,SAA2B,CACzBjM,OAAO,CAACkM,OAAO,CAAC,eAAe,CAAC,CAClC,CAAC,IAAM,CACLlM,OAAO,CAACmM,YAAY,CAAC,CAAC,CACxB,CACF,CACF,CAAC,CAEL,CAAC,CACD,OACF,CACF,CAGA,GAAI,CACFhJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC1C,KAAM,CAAAoJ,eAAe,CAAG,KAAM,CAAA9M,QAAQ,CAAC+M,uBAAuB,CAAC,CAC7DC,QAAQ,CAAEhN,QAAQ,CAACiN,QAAQ,CAACC,QAC9B,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAG,CACxBC,QAAQ,CAAEN,eAAe,CAACO,MAAM,CAACD,QAAQ,CACzCE,SAAS,CAAER,eAAe,CAACO,MAAM,CAACC,SACpC,CAAC,CAED7J,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEE,IAAI,CAACqD,SAAS,CAACkG,iBAAiB,CAAC,CAAC,CAC5EpL,WAAW,CAACoL,iBAAiB,CAAC,CAG9B,KAAM,CAAAjN,YAAY,CAAC+E,OAAO,CAAC,mBAAmB,CAAErB,IAAI,CAACqD,SAAS,CAACkG,iBAAiB,CAAC,CAAC,CAGlF,GAAI7K,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,EAAIzF,IAAI,CAAE,CAC5DY,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,gBAAgB,CAAE,CACvCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfhC,QAAQ,CAAEqL,iBACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAOI,YAAY,CAAE,CACrB9J,OAAO,CAACM,KAAK,CAAC,iCAAiC,CAAEwJ,YAAY,CAAC,CAE9D,GAAI,CACF,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAtN,YAAY,CAACiD,OAAO,CAAC,mBAAmB,CAAC,CACtE,GAAIqK,cAAc,CAAE,CAClB,KAAM,CAAAC,cAAc,CAAG7J,IAAI,CAACC,KAAK,CAAC2J,cAAc,CAAC,CACjD/J,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEE,IAAI,CAACqD,SAAS,CAACwG,cAAc,CAAC,CAAC,CACrE1L,WAAW,CAAC0L,cAAc,CAAC,CAC7B,CACF,CAAE,MAAOC,YAAY,CAAE,CACrBjK,OAAO,CAACM,KAAK,CAAC,gCAAgC,CAAE2J,YAAY,CAAC,CAC/D,CACF,CAGAjK,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3CiK,eAAe,CAAG,KAAM,CAAA3N,QAAQ,CAAC4N,kBAAkB,CACjD,CACEZ,QAAQ,CAAEhN,QAAQ,CAACiN,QAAQ,CAACY,IAAI,CAChCC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,IAChB,CAAC,CACAjM,QAAQ,EAAK,CACZ,KAAM,CAAAkM,WAAW,CAAG,CAClBZ,QAAQ,CAAEtL,QAAQ,CAACuL,MAAM,CAACD,QAAQ,CAClCE,SAAS,CAAExL,QAAQ,CAACuL,MAAM,CAACC,SAC7B,CAAC,CAED7J,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEE,IAAI,CAACqD,SAAS,CAAC+G,WAAW,CAAC,CAAC,CAC7DjM,WAAW,CAACiM,WAAW,CAAC,CAGxB9N,YAAY,CAAC+E,OAAO,CAAC,mBAAmB,CAAErB,IAAI,CAACqD,SAAS,CAAC+G,WAAW,CAAC,CAAC,CACnEC,KAAK,CAACC,GAAG,EAAIzK,OAAO,CAACM,KAAK,CAAC,wBAAwB,CAAEmK,GAAG,CAAC,CAAC,CAG7D,GAAI5L,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,EAAIzF,IAAI,CAAE,CAC5DY,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,gBAAgB,CAAE,CACvCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfhC,QAAQ,CAAEkM,WACZ,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAEDvK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAE9C,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD1D,KAAK,CAAC2K,KAAK,CACT,gBAAgB,CAChB,4GACF,CAAC,CACH,CACF,CAAC,CAGD,KAAM,CAAAiB,WAAW,CAAGA,CAAA,GAAM,CAExB,GAAIrK,aAAa,EAAI,CAACF,IAAI,EAAI,CAACA,IAAI,CAACoC,EAAE,CAAE,CACtC,KAAM,CAAAyF,MAAM,CAAG3H,aAAa,CAAG,iBAAiB,CAAG,iBAAiB,CACpE6B,OAAO,CAACC,GAAG,CAAC,mBAAmB6F,MAAM,EAAE,CAAC,CACxC,OACF,CAEA9F,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAItC,KAAM,CAAAyK,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAEF,KAAM,CAAAX,cAAc,CAAG,KAAM,CAAAtN,YAAY,CAACiD,OAAO,CAAC,mBAAmB,CAAC,CACtE,GAAIqK,cAAc,CAAE,CAClB,KAAM,CAAAC,cAAc,CAAG7J,IAAI,CAACC,KAAK,CAAC2J,cAAc,CAAC,CACjD/J,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEE,IAAI,CAACqD,SAAS,CAACwG,cAAc,CAAC,CAAC,CAGzE1L,WAAW,CAAC0L,cAAc,CAAC,CAG3B,MAAO,CAAAW,YAAY,CAACX,cAAc,CAAC,CACrC,CACF,CAAE,MAAO1J,KAAK,CAAE,CACdN,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACzD,CAGA,GAAI,CAAC5B,QAAQ,CAAE,CAEb,GAAI,CACF2B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CACxC,KAAM,CAAA2K,eAAe,CAAG,KAAM,CAAArO,QAAQ,CAAC+M,uBAAuB,CAAC,CAC7DC,QAAQ,CAAEhN,QAAQ,CAACiN,QAAQ,CAACC,QAC9B,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAG,CACxBC,QAAQ,CAAEiB,eAAe,CAAChB,MAAM,CAACD,QAAQ,CACzCE,SAAS,CAAEe,eAAe,CAAChB,MAAM,CAACC,SACpC,CAAC,CAED7J,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEE,IAAI,CAACqD,SAAS,CAACkG,iBAAiB,CAAC,CAAC,CAG7E,KAAM,CAAAjN,YAAY,CAAC+E,OAAO,CAAC,mBAAmB,CAAErB,IAAI,CAACqD,SAAS,CAACkG,iBAAiB,CAAC,CAAC,CAClFpL,WAAW,CAACoL,iBAAiB,CAAC,CAG9B,MAAO,CAAAiB,YAAY,CAACjB,iBAAiB,CAAC,CACxC,CAAE,MAAOmB,aAAa,CAAE,CACtB7K,OAAO,CAACM,KAAK,CAAC,iCAAiC,CAAEuK,aAAa,CAAC,CAC/DjO,KAAK,CAAC2K,KAAK,CACT,mBAAmB,CACnB,oEAAoE,CACpE,CACE,CAAE3C,IAAI,CAAE,IAAK,CAAC,CACd,CACEA,IAAI,CAAE,eAAe,CACrBkE,OAAO,CAAEA,CAAA,GAAM,CACb,SAA2B,CACzBjM,OAAO,CAACkM,OAAO,CAAC,eAAe,CAAC,CAClC,CAAC,IAAM,CACLlM,OAAO,CAACmM,YAAY,CAAC,CAAC,CACxB,CACF,CACF,CAAC,CAEL,CAAC,CACD5K,gBAAgB,CAAC,KAAK,CAAC,CACvB,OACF,CACF,CAGA,MAAO,CAAAuM,YAAY,CAACtM,QAAQ,CAAC,CAC/B,CAAC,CAGDqM,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAGD,KAAM,CAAAC,YAAY,CAAIC,eAAe,EAAK,CACxCxM,gBAAgB,CAAC,IAAI,CAAC,CAGtB4B,OAAO,CAACC,GAAG,CAAC,4BAA4BhC,IAAI,CAAC+C,QAAQ,SAAS/C,IAAI,CAACoC,EAAE,EAAE,CAAC,CAGxE,GAAI,CAACxB,SAAS,CAACwD,OAAO,EAAI,CAACxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CACtD1D,OAAO,CAACM,KAAK,CAAC,kDAAkD,CAAC,CAEjE,GAAIxB,aAAa,CAAE,CACjBD,SAAS,CAACwD,OAAO,CAAG7F,EAAE,CAAC,UAAUsC,aAAa,EAAE,CAAC,CACjDD,SAAS,CAACwD,OAAO,CAACiE,EAAE,CAAC,SAAS,CAAE,IAAM,CACpCtG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACnDpB,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,UAAU,CAAE,CACjCZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfW,QAAQ,CAAE/C,IAAI,CAAC+C,QACjB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEApE,KAAK,CAAC2K,KAAK,CACT,kBAAkB,CAClB,iEACF,CAAC,CAEDnJ,gBAAgB,CAAC,KAAK,CAAC,CACvB,OACF,CAGA,GAAIL,UAAU,EAAIc,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CAAE,CAClE1D,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC,CAC5CpB,SAAS,CAACwD,OAAO,CAACsB,IAAI,CAAC,OAAO,CAAE,CAC9BZ,MAAM,CAAE9E,IAAI,CAACoC,EAAE,CACfW,QAAQ,CAAE/C,IAAI,CAAC+C,QAAQ,CACvB3C,QAAQ,CAAEuM,eAAe,CACzBrM,WAAW,CACXsG,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCnG,YAAY,CACZmM,cAAc,CAAE,IAAI,CACpB1L,MAAM,CAAEA,MAAM,CACdE,MAAM,CAAEA,MACV,CAAC,CAAC,CAEFU,OAAO,CAACC,GAAG,CAAC,iCAAiChC,IAAI,CAACoC,EAAE,eAAepC,IAAI,CAAC+C,QAAQ,aAAa,CAAEb,IAAI,CAACqD,SAAS,CAACoH,eAAe,CAAC,CAAC,CACjI,CAAC,IAAM,CACL5K,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAE,CACtClC,UAAU,CACVgN,eAAe,CAAElM,SAAS,CAACwD,OAAO,EAAIxD,SAAS,CAACwD,OAAO,CAACqB,SAAS,CACjEsH,WAAW,CAAE,CAAC,CAACJ,eACjB,CAAC,CAAC,CACJ,CAGAtI,UAAU,CAAC,IAAM,CACftC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAC9C7B,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAGD,KAAM,CAAA6M,WAAW,CAAG,KAAAA,CAAO/K,QAAQ,CAAEgL,MAAM,GAAK,CAC9ClL,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEE,IAAI,CAACqD,SAAS,CAACtD,QAAQ,CAAC,CAAC,CAC3DF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEiL,MAAM,CAAC,CAGvC,GAAI,CAAChL,QAAQ,CAAE,CACbF,OAAO,CAACM,KAAK,CAAC,sCAAsC,CAAC,CACrD1D,KAAK,CAAC2K,KAAK,CAAC,aAAa,CAAE,+CAA+C,CAAC,CAC3E,OACF,CAEA,GAAI,MAAO,CAAArH,QAAQ,GAAK,QAAQ,CAAE,CAChCF,OAAO,CAACM,KAAK,CAAC,yCAAyC,CAAE,MAAO,CAAAJ,QAAQ,CAAC,CACzEtD,KAAK,CAAC2K,KAAK,CAAC,aAAa,CAAE,sDAAsD,CAAC,CAClF,OACF,CAGA,KAAM,CAAAxE,MAAM,CAAG7C,QAAQ,CAACG,EAAE,CAC1B,GAAI,CAAC0C,MAAM,CAAE,CACX/C,OAAO,CAACM,KAAK,CAAC,gCAAgC,CAAEJ,QAAQ,CAAC,CACzDtD,KAAK,CAAC2K,KAAK,CAAC,aAAa,CAAE,oDAAoD,CAAC,CAChF,OACF,CAEAvH,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE8C,MAAM,CAAC,CAGjD,KAAM,CAAA9E,IAAI,CAAG,CACXoC,EAAE,CAAE0C,MAAM,CACV/B,QAAQ,CAAEd,QAAQ,CAACc,QAAQ,EAAI,MACjC,CAAC,CAEDhB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEE,IAAI,CAACqD,SAAS,CAACvF,IAAI,CAAC,CAAC,CAC3DC,OAAO,CAACD,IAAI,CAAC,CAEb+B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzCjC,aAAa,CAAC,IAAI,CAAC,CAEnBgC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEiL,MAAM,CAAC,CAChDnM,gBAAgB,CAACmM,MAAM,CAAC,CAExB,GAAI,CAEF,KAAM,CAAAC,WAAW,CAAGhL,IAAI,CAACqD,SAAS,CAACvF,IAAI,CAAC,CACxC+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEkL,WAAW,CAAC,CAGpD,GAAI,CACF,KAAM,CAAA1O,YAAY,CAAC+E,OAAO,CAAC,MAAM,CAAE2J,WAAW,CAAC,CAC/CnL,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAC5D,CAAE,MAAOmL,SAAS,CAAE,CAClBpL,OAAO,CAACM,KAAK,CAAC,wCAAwC,CAAE8K,SAAS,CAAC,CACpE,CAEA,GAAI,CACF,KAAM,CAAA3O,YAAY,CAAC+E,OAAO,CAAC,eAAe,CAAE0J,MAAM,CAAC,CACnDlL,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CACjE,CAAE,MAAOoL,WAAW,CAAE,CACpBrL,OAAO,CAACM,KAAK,CAAC,6CAA6C,CAAE+K,WAAW,CAAC,CAC3E,CAGA,GAAI,CACF,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAA7O,YAAY,CAACiD,OAAO,CAAC,MAAM,CAAC,CACrDM,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEqL,UAAU,CAAC,CAC7D,CAAE,MAAOC,WAAW,CAAE,CACpBvL,OAAO,CAACM,KAAK,CAAC,mCAAmC,CAAEiL,WAAW,CAAC,CACjE,CACF,CAAE,MAAOjL,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CAEAN,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAChD,CAAC,CAGD,KAAM,CAAAuL,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI3M,SAAS,CAACwD,OAAO,CAAE,CACrBxD,SAAS,CAACwD,OAAO,CAACmF,UAAU,CAAC,CAAC,CAChC,CAGA,KAAM,CAAAiE,aAAa,CAAG3M,aAAa,CAGnCZ,OAAO,CAAC,IAAI,CAAC,CACbF,aAAa,CAAC,KAAK,CAAC,CACpBU,UAAU,CAAC,EAAE,CAAC,CACdE,eAAe,CAAC,EAAE,CAAC,CAEnB,GAAI,CAEF,KAAM,CAAAnC,YAAY,CAAC8D,UAAU,CAAC,MAAM,CAAC,CAGrCxB,gBAAgB,CAAC0M,aAAa,CAAC,CACjC,CAAE,MAAOnL,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,CAGD,KAAM,CAAAoL,iBAAiB,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAC5CnN,cAAc,CAACmN,QAAQ,CAAC,CACxB,GAAI,CACF,KAAM,CAAAlP,YAAY,CAAC+E,OAAO,CAAC,aAAa,CAAEmK,QAAQ,CAAClK,QAAQ,CAAC,CAAC,CAAC,CAChE,CAAE,MAAOnB,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAAC,CAED,MACE,CAAA1C,KAAA,CAACjB,IAAI,EAACiP,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAE,CAAE,CAAAC,QAAA,EACvBtO,IAAA,CAACd,SAAS,EAACqP,QAAQ,CAAC,cAAc,CAACC,eAAe,CAAC,MAAM,CAAE,CAAC,CAE5DxO,IAAA,CAACpB,mBAAmB,EAAC6P,GAAG,CAAEjN,aAAc,CAAA8M,QAAA,CACtCtO,IAAA,CAACK,KAAK,CAACqO,SAAS,EACdC,gBAAgB,CAAEpO,UAAU,CAAG,MAAM,CAAG,OAAQ,CAChDqO,aAAa,CAAE,CACbC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,CACXN,eAAe,CAAE,MAAM,CACvBO,SAAS,CAAE,CAAC,CACZC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CAAC,CACpBC,iBAAiB,CAAE,MACrB,CAAC,CACDC,gBAAgB,CAAE,CAChBC,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE,SACT,CAAC,CACDC,eAAe,CAAE,SACnB,CAAE,CAAAhB,QAAA,CAED,CAAC/N,UAAU,CAEVH,KAAA,CAAAF,SAAA,EAAAoO,QAAA,EACEtO,IAAA,CAACK,KAAK,CAACkP,MAAM,EACXrK,IAAI,CAAC,OAAO,CACZsK,OAAO,CAAE,CACPC,KAAK,CAAE,SAAS,CAChBZ,WAAW,CAAE,KACf,CAAE,CAAAP,QAAA,CAEDoB,KAAK,EACJ1P,IAAA,CAACT,WAAW,CAAAyI,MAAA,CAAAC,MAAA,IACNyH,KAAK,EACTC,OAAO,CAAElC,WAAY,CACrBnM,aAAa,CAAEA,aAAc,EAC9B,CACF,CACW,CAAC,CACftB,IAAA,CAACK,KAAK,CAACkP,MAAM,EACXrK,IAAI,CAAC,UAAU,CACfsK,OAAO,CAAE,CACPC,KAAK,CAAE,gBAAgB,CACvBZ,WAAW,CAAE,KACf,CAAE,CAAAP,QAAA,CAEDoB,KAAK,EACJ1P,IAAA,CAACR,cAAc,CAAAwI,MAAA,CAAAC,MAAA,IACTyH,KAAK,EACTpO,aAAa,CAAEA,aAAc,EAC9B,CACF,CACW,CAAC,EACf,CAAC,CAEHlB,KAAA,CAAAF,SAAA,EAAAoO,QAAA,EACEtO,IAAA,CAACK,KAAK,CAACkP,MAAM,EACXrK,IAAI,CAAC,MAAM,CACXsK,OAAO,CAAE,CAAEC,KAAK,CAAE,eAAgB,CAAE,CAAAnB,QAAA,CAEnCoB,KAAK,EACJ1P,IAAA,CAACP,UAAU,CAAAuI,MAAA,CAAAC,MAAA,IACLyH,KAAK,EACTjP,IAAI,CAAEA,IAAK,CACXE,aAAa,CAAEA,aAAc,CAC7BM,OAAO,CAAEA,OAAQ,CACjB2O,OAAO,CAAE5E,WAAY,CACrB6E,aAAa,CAAE5J,gBAAiB,CAChC6J,WAAW,CAAEhK,eAAgB,CAC7BxE,aAAa,CAAEA,aAAc,EAC9B,CACF,CACW,CAAC,CAGftB,IAAA,CAACK,KAAK,CAACkP,MAAM,EAACrK,IAAI,CAAC,UAAU,CAAAoJ,QAAA,CAC1BoB,KAAK,EAAI1P,IAAA,CAACN,cAAc,CAAAsI,MAAA,CAAAC,MAAA,IAAKyH,KAAK,EAAE3O,WAAW,CAAEA,WAAY,CAACa,MAAM,CAAEA,MAAO,CAACE,MAAM,CAAEA,MAAO,CAACX,YAAY,CAAEA,YAAa,CAAC4O,mBAAmB,CAAE7B,iBAAkB,CAAC8B,gBAAgB,CAAEnM,cAAe,CAACoM,aAAa,CAAE5J,iBAAkB,CAAC6J,QAAQ,CAAElC,YAAa,EAAE,CAAC,CACrP,CAAC,CACfhO,IAAA,CAACK,KAAK,CAACkP,MAAM,EAACrK,IAAI,CAAC,MAAM,CAAAoJ,QAAA,CACtBoB,KAAK,EAAI1P,IAAA,CAACL,UAAU,CAAAqI,MAAA,CAAAC,MAAA,IAAKyH,KAAK,EAAES,MAAM,CAAE9O,SAAS,CAACwD,OAAQ,CAACpE,IAAI,CAAEA,IAAK,CAAC2P,YAAY,CAAEnK,gBAAiB,CAAC6J,WAAW,CAAEhK,eAAgB,EAAE,CAAC,CAC5H,CAAC,CACf9F,IAAA,CAACK,KAAK,CAACkP,MAAM,EAACrK,IAAI,CAAC,OAAO,CAAAoJ,QAAA,CACvBoB,KAAK,EAAI1P,IAAA,CAACJ,WAAW,CAAAoI,MAAA,CAAAC,MAAA,IAAKyH,KAAK,EAAEpO,aAAa,CAAEA,aAAc,EAAE,CAAC,CACtD,CAAC,CACftB,IAAA,CAACK,KAAK,CAACkP,MAAM,EAACrK,IAAI,CAAC,aAAa,CAACmL,SAAS,CAAExQ,iBAAkB,CAAE,CAAC,CACjEG,IAAA,CAACK,KAAK,CAACkP,MAAM,EAACrK,IAAI,CAAC,eAAe,CAAAoJ,QAAA,CAC7CoB,KAAK,EACJ1P,IAAA,CAACF,aAAa,CAAAkI,MAAA,CAAAC,MAAA,IACRyH,KAAK,EACTjP,IAAI,CAAEA,IAAK,CACXa,aAAa,CAAEA,aAAc,CAC7BgP,eAAe,CAAEjL,mBAAoB,CACrCD,cAAc,CAAEsK,KAAK,CAACa,KAAK,CAACpL,MAAM,EAAEC,cAAc,EAAI,KAAM,EAC7D,CACF,CACW,CAAC,EACD,CACH,CACc,CAAC,CACC,CAAC,EAClB,CAAC,CAEX", "ignoreList": []}, "metadata": {"hasCjsExports": false}, "sourceType": "module", "externalDependencies": []}