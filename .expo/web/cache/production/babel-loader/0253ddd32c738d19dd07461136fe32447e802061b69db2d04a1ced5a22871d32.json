{"ast": null, "code": "import'./timer-polyfills';import AppRegistry from\"react-native-web/dist/exports/AppRegistry\";import App from'./App.js';console.log('Starting app with navigation');AppRegistry.registerComponent('main',()=>App);console.log('App registered successfully');", "map": {"version": 3, "names": ["AppRegistry", "App", "console", "log", "registerComponent"], "sources": ["/Users/<USER>/Desktop/Shake2/index.js"], "sourcesContent": ["// index.js - Updated for navigation test\n// Import polyfills first\nimport './timer-polyfills';\n\n// Standard imports\nimport { AppRegistry } from 'react-native';\n\n// Change this line to test different app versions\nimport App from './App.js';\n// import App from './UltraMinimalApp'; \n\nconsole.log('Starting app with navigation');\n\n// Register with the name \"main\"\nAppRegistry.registerComponent('main', () => App);\n\nconsole.log('App registered successfully');"], "mappings": "AAEA,MAAO,mBAAmB,CAAC,OAAAA,WAAA,iDAM3B,MAAO,CAAAC,GAAG,KAAM,UAAU,CAG1BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAG3CH,WAAW,CAACI,iBAAiB,CAAC,MAAM,CAAE,IAAMH,GAAG,CAAC,CAEhDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC", "ignoreList": []}, "metadata": {"hasCjsExports": false}, "sourceType": "module", "externalDependencies": []}