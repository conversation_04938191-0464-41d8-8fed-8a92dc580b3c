// test-social-auth.js
// Simple test to verify social auth configuration

console.log('Testing Social Auth Service without native modules...');

// Test that the service can be imported without errors
try {
  // This should not throw the TurboModuleRegistry error anymore
  const SocialAuthService = require('./services/SocialAuthService').default;
  console.log('✅ SocialAuthService imported successfully');

  // Test getting available providers
  SocialAuthService.getAvailableProviders().then(providers => {
    console.log('✅ Available providers:', providers);
  }).catch(error => {
    console.error('❌ Error getting providers:', error);
  });

  // Test initialization
  SocialAuthService.initializeProviders().then(() => {
    console.log('✅ Social auth service initialized successfully');
  }).catch(error => {
    console.error('❌ Error initializing providers:', error);
  });

} catch (error) {
  console.error('❌ Failed to import SocialAuthService:', error);
}
