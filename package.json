{"dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "^4.5.6", "@react-navigation/elements": "^2.2.5", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "expo": "^53.0.9", "expo-apple-authentication": "~7.2.4", "expo-auth-session": "~6.2.1", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.1.8", "expo-image-picker": "~16.1.4", "expo-location": "~18.1.4", "expo-sensors": "~14.1.4", "expo-status-bar": "~2.2.3", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "geolib": "^3.3.4", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@faker-js/faker": "^8.4.1", "@react-native-community/cli": "latest", "artillery": "^2.0.0", "axios": "^1.6.0", "bcrypt": "^5.1.1", "mongoose": "^8.18.0", "socket.io-client": "^4.8.1", "uuid": "^9.0.1", "ws": "^8.14.0"}, "name": "shake2", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo build:web", "load-test": "node load-tests/orchestrator.js", "load-test:registration": "node load-tests/registration-test.js", "load-test:profiles": "node load-tests/profile-test.js", "load-test:sockets": "node load-tests/socket-test.js", "load-test:matching": "node load-tests/matching-test.js", "load-test:messaging": "node load-tests/messaging-test.js", "load-test:full": "node load-tests/full-scenario-test.js"}}