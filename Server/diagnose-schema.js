// diagnose-schema.js - Deep dive into the schema and database issue
const mongoose = require('mongoose');

async function diagnoseSchema() {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch';
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Get direct access to the collection
    const db = mongoose.connection.db;
    const collection = db.collection('users');

    console.log('\n🔍 Raw database inspection:');
    
    // Find the admin user directly from MongoDB
    const rawUser = await collection.findOne({ username: 'admin' });
    console.log('Raw admin user from MongoDB:');
    console.log(JSON.stringify(rawUser, null, 2));

    // Find the newadmin user directly from MongoDB
    const rawNewUser = await collection.findOne({ username: 'newadmin' });
    console.log('\nRaw newadmin user from MongoDB:');
    console.log(JSON.stringify(rawNewUser, null, 2));

    // Check collection schema/indexes
    console.log('\n📋 Collection indexes:');
    const indexes = await collection.indexes();
    console.log(JSON.stringify(indexes, null, 2));

    // Try a direct insert with role field
    console.log('\n🧪 Testing direct insert with role field...');
    try {
      const testResult = await collection.insertOne({
        username: 'testadmin',
        password: 'hashedpassword',
        role: 'admin',
        accountStatus: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('Direct insert result:', testResult);

      // Check if it was inserted correctly
      const testUser = await collection.findOne({ username: 'testadmin' });
      console.log('Test user after insert:');
      console.log(JSON.stringify(testUser, null, 2));

      // Clean up test user
      await collection.deleteOne({ username: 'testadmin' });
      console.log('Test user cleaned up');

    } catch (insertError) {
      console.log('Direct insert failed:', insertError.message);
    }

    // Try updating the newadmin user directly
    console.log('\n🔧 Direct update of newadmin user...');
    const updateResult = await collection.updateOne(
      { username: 'newadmin' },
      { 
        $set: { 
          role: 'admin',
          accountStatus: 'active',
          updatedAt: new Date(),
          testField: 'this is a test'
        }
      }
    );
    console.log('Direct update result:', updateResult);

    // Check the result
    const updatedRawUser = await collection.findOne({ username: 'newadmin' });
    console.log('Updated newadmin user:');
    console.log(JSON.stringify(updatedRawUser, null, 2));

    // Now let's check what the Mongoose model sees
    console.log('\n🔍 Mongoose model inspection:');
    const User = require('./models/User');
    
    const mongooseUser = await User.findOne({ username: 'newadmin' });
    console.log('Mongoose view of newadmin:');
    console.log('Username:', mongooseUser.username);
    console.log('Role:', mongooseUser.role);
    console.log('AccountStatus:', mongooseUser.accountStatus);
    console.log('TestField:', mongooseUser.testField);
    console.log('Full object:', JSON.stringify(mongooseUser.toObject(), null, 2));

    // Check the schema
    console.log('\n📝 User schema paths:');
    const schema = User.schema;
    console.log('Schema paths:', Object.keys(schema.paths));
    console.log('Role field definition:', schema.paths.role);
    console.log('AccountStatus field definition:', schema.paths.accountStatus);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

diagnoseSchema();
