#!/bin/bash

# MongoDB Security Configuration Script
# This script will secure your MongoDB instance

echo "🔒 MongoDB Security Configuration"
echo "================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "⚠️  WARNING: This will secure your MongoDB instance and may break existing connections"
echo "Make sure you have backups and understand the changes being made."
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Stop MongoDB service
echo "🛑 Stopping MongoDB service..."
systemctl stop mongod

# Backup original configuration
echo "💾 Backing up original MongoDB configuration..."
cp /etc/mongod.conf /etc/mongod.conf.backup.$(date +%Y%m%d_%H%M%S)

# Create secure MongoDB configuration
echo "🔧 Creating secure MongoDB configuration..."
cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - Secured

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - SECURED
net:
  port: 27017
  bindIp: 127.0.0.1  # Only allow local connections
  # bindIp: 127.0.0.1,YOUR_APP_SERVER_IP  # Add your app server IP if needed

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - ENABLED
security:
  authorization: enabled

# Operation profiling
#operationProfiling:

# Replication
#replication:

# Sharding
#sharding:
EOF

echo "✅ Secure configuration created"

# Create MongoDB admin user
echo "👤 Creating MongoDB admin user..."
systemctl start mongod
sleep 5

# Create admin user
mongosh --eval "
use admin;
db.createUser({
  user: 'admin',
  pwd: 'SecurePassword123!',
  roles: [
    { role: 'userAdminAnyDatabase', db: 'admin' },
    { role: 'readWriteAnyDatabase', db: 'admin' },
    { role: 'dbAdminAnyDatabase', db: 'admin' }
  ]
});
print('✅ Admin user created');
"

# Create application user for your app
mongosh --eval "
use shakeAndMatch;
db.createUser({
  user: 'shakeapp',
  pwd: 'ShakeApp123!',
  roles: [
    { role: 'readWrite', db: 'shakeAndMatch' }
  ]
});
print('✅ Application user created');
"

# Restart MongoDB with authentication enabled
echo "🔄 Restarting MongoDB with authentication..."
systemctl restart mongod

# Configure firewall
echo "🔥 Configuring firewall..."
ufw --force enable
ufw delete allow 27017  # Remove any existing MongoDB rules
ufw allow from 127.0.0.1 to any port 27017  # Only allow local connections
echo "✅ Firewall configured"

# Display connection information
echo ""
echo "🎉 MongoDB Security Configuration Complete!"
echo "=========================================="
echo ""
echo "📋 Important Information:"
echo "• MongoDB now requires authentication"
echo "• Only local connections (127.0.0.1) are allowed"
echo "• Admin user: admin / SecurePassword123!"
echo "• App user: shakeapp / ShakeApp123!"
echo ""
echo "🔗 New connection strings:"
echo "Admin: mongodb://admin:SecurePassword123!@127.0.0.1:27017/admin"
echo "App: mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch"
echo ""
echo "⚠️  IMPORTANT: Update your application to use the new connection string!"
echo "⚠️  CHANGE THE DEFAULT PASSWORDS immediately!"
echo ""
echo "🔍 To check MongoDB status: sudo systemctl status mongod"
echo "📝 To view MongoDB logs: sudo tail -f /var/log/mongodb/mongod.log"
