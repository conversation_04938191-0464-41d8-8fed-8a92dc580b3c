<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shake & Match - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #0f172a;
            color: #e2e8f0;
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .app-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #1e293b;
            border-right: 1px solid #334155;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #334155;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
            color: #f1f5f9;
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: #94a3b8;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: #334155;
            color: #f1f5f9;
        }

        .nav-item.active {
            background: #1e40af;
            color: white;
            border-right: 3px solid #3b82f6;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            background: #0f172a;
        }

        .top-bar {
            background: #1e293b;
            border-bottom: 1px solid #334155;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #f1f5f9;
            margin: 0;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #94a3b8;
            font-size: 0.9rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .content-area {
            padding: 2rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .header-left h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .header-left p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            text-align: right;
        }

        .user-info .username {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .user-info .role {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .header h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            text-align: center;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5, #7c3aed);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 16px;
            padding: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow-x: auto;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav-tab {
            flex: 1;
            padding: 1rem 1.5rem;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
            color: #64748b;
            position: relative;
        }

        .nav-tab:hover {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .data-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .table-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a202c;
        }

        .search-box {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 0.9rem;
            width: 280px;
            background: white;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .table-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 0.9rem;
            background: white;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .bulk-actions {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .selected-count {
            font-weight: 600;
            color: #667eea;
        }

        .bulk-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            gap: 0.5rem;
        }

        .pagination button {
            padding: 0.5rem 0.75rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-blocked {
            background: #f8d7da;
            color: #721c24;
        }

        .status-deleted {
            background: #d1ecf1;
            color: #0c5460;
        }

        .role-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-admin {
            background: #fff3cd;
            color: #856404;
        }

        .role-user {
            background: #e2e3e5;
            color: #383d41;
        }

        .online-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .online-indicator.online {
            background: #28a745;
        }

        .online-indicator.offline {
            background: #6c757d;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .user-images {
            display: flex;
            gap: 0.5rem;
        }

        .user-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }

        .passions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .passion-tag {
            background: #e9ecef;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            color: #495057;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        /* Form Styles */
        .user-form {
            margin-top: 1.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-error {
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: none;
        }

        .form-error.show {
            display: block;
        }

        .form-help {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .passion-selector {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem;
            background: white;
            transition: all 0.3s ease;
        }

        .passion-selector:focus-within {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        #passionInput {
            border: none;
            outline: none;
            width: 100%;
            padding: 0.5rem 0;
            font-size: 0.9rem;
        }

        .selected-passions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .passion-chip {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .passion-chip .remove {
            cursor: pointer;
            font-weight: bold;
            opacity: 0.8;
        }

        .passion-chip .remove:hover {
            opacity: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .header-left h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }

            .table-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }

            .search-box {
                width: 100%;
            }

            .filter-select {
                width: 100%;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .nav-tabs {
                padding: 4px;
            }

            .nav-tab {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            th, td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.7rem;
            }

            .modal-content {
                margin: 2% auto;
                width: 95%;
                max-height: 95vh;
            }
        }

        @media (max-width: 480px) {
            .header-left h1 {
                font-size: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(4px);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Enhanced table styles */
        .table-wrapper {
            overflow-x: auto;
            border-radius: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-weight: 700;
            color: #374151;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 1rem 1.5rem;
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Enhanced badges */
        .status-badge,
        .role-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .status-blocked {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .status-deleted {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .role-admin {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .role-user {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-left">
                    <h1>🤝 Shake & Match</h1>
                    <p>Admin Dashboard</p>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <div class="username" id="currentUsername">Loading...</div>
                        <div class="role" id="currentRole">Admin</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">
                        🚪 Logout
                    </button>
                </div>
            </div>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">-</div>
                <div class="stat-label">Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMatches">-</div>
                <div class="stat-label">Total Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">-</div>
                <div class="stat-label">Messages Sent</div>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('users')">👥 Users</button>
            <button class="nav-tab" onclick="showTab('matches')">💕 Matches</button>
            <button class="nav-tab" onclick="showTab('messages')">💬 Messages</button>
            <button class="nav-tab" onclick="showTab('analytics')">📊 Analytics</button>
        </div>

        <div id="users" class="tab-content active">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">User Management</div>
                    <div class="table-controls">
                        <input type="text" class="search-box" placeholder="Search users..." id="userSearch">
                        <select class="filter-select" id="roleFilter">
                            <option value="">All Roles</option>
                            <option value="user">Users</option>
                            <option value="admin">Admins</option>
                        </select>
                        <select class="filter-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="blocked">Blocked</option>
                            <option value="deleted">Deleted</option>
                        </select>
                        <button class="btn btn-primary" onclick="showCreateUserModal()">➕ Add User</button>
                    </div>
                </div>

                <div class="bulk-actions" id="bulkActions" style="display: none;">
                    <span class="selected-count" id="selectedCount">0 selected</span>
                    <select class="bulk-select" id="bulkActionSelect">
                        <option value="">Bulk Actions</option>
                        <option value="block">Block Selected</option>
                        <option value="unblock">Unblock Selected</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button class="btn btn-warning" onclick="executeBulkAction()">Apply</button>
                    <button class="btn btn-secondary" onclick="clearSelection()">Clear</button>
                </div>

                <div class="table-wrapper">
                    <table id="usersTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortUsers('username')">Username ↕</th>
                                <th onclick="sortUsers('email')">Email ↕</th>
                                <th onclick="sortUsers('role')">Role ↕</th>
                                <th onclick="sortUsers('accountStatus')">Status ↕</th>
                                <th onclick="sortUsers('age')">Age ↕</th>
                                <th>Passions</th>
                                <th>Images</th>
                                <th onclick="sortUsers('createdAt')">Created ↕</th>
                                <th onclick="sortUsers('lastLogin')">Last Login ↕</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr><td colspan="11">Loading users...</td></tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="usersPagination">
                    <!-- Pagination will be inserted here -->
                </div>
            </div>
        </div>

        <div id="matches" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Match History</div>
                    <input type="text" class="search-box" placeholder="Search matches..." id="matchSearch" onkeyup="filterTable('matchesTable', this.value)">
                </div>
                <div class="table-wrapper">
                    <table id="matchesTable">
                        <thead>
                            <tr>
                                <th>Match ID</th>
                                <th>User 1</th>
                                <th>User 2</th>
                                <th>Distance (km)</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="matchesTableBody">
                            <tr><td colspan="6" class="loading">Loading matches...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="messages" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Message History</div>
                    <input type="text" class="search-box" placeholder="Search messages..." id="messageSearch" onkeyup="filterTable('messagesTable', this.value)">
                </div>
                <div class="table-wrapper">
                    <table id="messagesTable">
                        <thead>
                            <tr>
                                <th>From</th>
                                <th>To</th>
                                <th>Message</th>
                                <th>Timestamp</th>
                                <th>Read</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                            <tr><td colspan="6" class="loading">Loading messages...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="analytics" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Analytics & Insights</div>
                </div>
                <div style="padding: 2rem;">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="avgAge">-</div>
                            <div class="stat-label">Average Age</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="avgDistance">-</div>
                            <div class="stat-label">Avg Match Distance</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="matchRate">-</div>
                            <div class="stat-label">Match Success Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="msgPerMatch">-</div>
                            <div class="stat-label">Messages per Match</div>
                        </div>
                    </div>
                    <div id="analyticsCharts" style="margin-top: 2rem;">
                        <h3>Popular Passions</h3>
                        <div id="passionsChart" style="background: white; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                            Loading analytics...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Detail Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('userModal')">&times;</span>
            <h2>User Details</h2>
            <div id="userModalContent">
                Loading...
            </div>
        </div>
    </div>

    <!-- Create/Edit User Modal -->
    <div id="userFormModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <span class="close" onclick="closeModal('userFormModal')">&times;</span>
            <h2 id="userFormTitle">Create New User</h2>
            <form id="userForm" class="user-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="formUsername">Username *</label>
                        <input type="text" id="formUsername" name="username" required>
                        <div class="form-error" id="usernameError"></div>
                    </div>
                    <div class="form-group">
                        <label for="formEmail">Email</label>
                        <input type="email" id="formEmail" name="email">
                        <div class="form-error" id="emailError"></div>
                    </div>
                    <div class="form-group">
                        <label for="formPassword">Password *</label>
                        <input type="password" id="formPassword" name="password" required>
                        <div class="form-error" id="passwordError"></div>
                    </div>
                    <div class="form-group">
                        <label for="formAge">Age</label>
                        <select id="formAge" name="age">
                            <option value="">Select Age</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="formRole">Role *</label>
                        <select id="formRole" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="formStatus">Account Status *</label>
                        <select id="formStatus" name="accountStatus" required>
                            <option value="active">Active</option>
                            <option value="blocked">Blocked</option>
                            <option value="deleted">Deleted</option>
                        </select>
                    </div>
                </div>
                <div class="form-group full-width">
                    <label for="formDescription">Description</label>
                    <textarea id="formDescription" name="description" rows="3" placeholder="Tell us about yourself..."></textarea>
                </div>
                <div class="form-group full-width">
                    <label for="formPassions">Passions</label>
                    <div id="passionSelector" class="passion-selector">
                        <input type="text" id="passionInput" placeholder="Add a passion and press Enter">
                        <div id="selectedPassions" class="selected-passions"></div>
                    </div>
                    <div class="form-help">Add 3-6 passions that describe your interests</div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('userFormModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="userFormSubmit">
                        <span class="btn-text">Create User</span>
                        <div class="spinner" style="display: none;"></div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin;

        // Global data storage
        let users = [];
        let matches = [];
        let messages = [];
        let activeUsersData = [];
        let currentUser = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
        });

        // Check authentication status
        async function checkAuthentication() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    // Not authenticated, redirect to login
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update header with user info
                document.getElementById('currentUsername').textContent = currentUser.username;
                document.getElementById('currentRole').textContent = currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);

                // Load dashboard data
                loadDashboardData();
                // Refresh data every 30 seconds
                setInterval(loadDashboardData, 30000);

            } catch (error) {
                console.error('Authentication check failed:', error);
                window.location.href = '/admin/login';
            }
        }

        // Logout function
        async function logout() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/logout`, {
                    method: 'POST',
                    credentials: 'include'
                });

                // Redirect to login regardless of response
                window.location.href = '/admin/login';
            } catch (error) {
                console.error('Logout error:', error);
                // Still redirect to login
                window.location.href = '/admin/login';
            }
        }

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadUsers(),
                    loadMatches(),
                    loadMessages(),
                    loadActiveUsers()
                ]);
                updateStats();
                updateAnalytics();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load users data
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                users = data.users || [];

                // Get detailed user data using admin-specific endpoint
                const detailedUsers = await Promise.all(
                    users.map(async (user) => {
                        try {
                            const profileResponse = await fetch(`${API_BASE}/api/admin/profile/${user.id}`);
                            const profileData = await profileResponse.json();
                            return { ...user, ...profileData.profile };
                        } catch (error) {
                            console.error(`Error loading profile for user ${user.id}:`, error);
                            return user;
                        }
                    })
                );

                users = detailedUsers;
                renderUsersTable();
            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('usersTableBody').innerHTML =
                    '<tr><td colspan="8" class="error">Error loading users</td></tr>';
            }
        }

        // Load matches data
        async function loadMatches() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/matches`, {
                    credentials: 'include'
                });
                const data = await response.json();
                matches = data.matches || [];
                renderMatchesTable();
            } catch (error) {
                console.error('Error loading matches:', error);
                document.getElementById('matchesTableBody').innerHTML =
                    '<tr><td colspan="6" class="error">Error loading matches</td></tr>';
            }
        }

        // Load messages data
        async function loadMessages() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/messages`, {
                    credentials: 'include'
                });
                const data = await response.json();
                messages = data.messages || [];
                renderMessagesTable();
            } catch (error) {
                console.error('Error loading messages:', error);
                document.getElementById('messagesTableBody').innerHTML =
                    '<tr><td colspan="6" class="error">Error loading messages</td></tr>';
            }
        }

        // Load active users
        async function loadActiveUsers() {
            try {
                const response = await fetch(`${API_BASE}/api/debug/active-users`);
                const data = await response.json();
                activeUsersData = data.users || [];
            } catch (error) {
                console.error('Error loading active users:', error);
                activeUsersData = [];
            }
        }

        // Update statistics
        function updateStats() {
            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = activeUsersData.length;
            document.getElementById('totalMatches').textContent = matches.length;
            document.getElementById('totalMessages').textContent = messages.length;
        }

        // Update analytics
        function updateAnalytics() {
            // Calculate average age
            const usersWithAge = users.filter(u => u.age);
            const avgAge = usersWithAge.length > 0
                ? Math.round(usersWithAge.reduce((sum, u) => sum + u.age, 0) / usersWithAge.length)
                : 0;
            document.getElementById('avgAge').textContent = avgAge || '-';

            // Calculate average match distance
            const avgDistance = matches.length > 0
                ? (matches.reduce((sum, m) => sum + m.distance, 0) / matches.length).toFixed(1)
                : 0;
            document.getElementById('avgDistance').textContent = avgDistance ? `${avgDistance}km` : '-';

            // Calculate match rate (placeholder)
            document.getElementById('matchRate').textContent = users.length > 0 ? `${Math.round((matches.length / users.length) * 100)}%` : '-';

            // Calculate messages per match
            const msgPerMatch = matches.length > 0 ? Math.round(messages.length / matches.length) : 0;
            document.getElementById('msgPerMatch').textContent = msgPerMatch || '-';

            // Render popular passions
            renderPopularPassions();
        }

        // Render popular passions chart
        function renderPopularPassions() {
            const passionCounts = {};
            users.forEach(user => {
                if (user.passions && Array.isArray(user.passions)) {
                    user.passions.forEach(passion => {
                        passionCounts[passion] = (passionCounts[passion] || 0) + 1;
                    });
                }
            });

            const sortedPassions = Object.entries(passionCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            const chartHtml = sortedPassions.length > 0
                ? sortedPassions.map(([passion, count]) => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <span>${passion}</span>
                        <span style="background: #667eea; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">${count}</span>
                    </div>
                `).join('')
                : '<p style="text-align: center; color: #666;">No passion data available</p>';

            document.getElementById('passionsChart').innerHTML = chartHtml;
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; color: #666;">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td><input type="checkbox" class="user-checkbox" value="${user.id}" onchange="updateBulkActions()"></td>
                    <td><strong>${user.username}</strong></td>
                    <td>${user.email || '-'}</td>
                    <td><span class="role-badge role-${user.role || 'user'}">${(user.role || 'user').toUpperCase()}</span></td>
                    <td><span class="status-badge status-${user.accountStatus || 'active'}">${(user.accountStatus || 'active').toUpperCase()}</span></td>
                    <td>${user.age || '-'}</td>
                    <td>
                        <div class="passions">
                            ${(user.passions || []).slice(0, 3).map(passion =>
                                `<span class="passion-tag">${passion}</span>`
                            ).join('')}
                            ${(user.passions || []).length > 3 ? `<span class="passion-tag">+${(user.passions || []).length - 3}</span>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="badge ${(user.imageCount || 0) > 0 ? 'badge-success' : 'badge-secondary'}">
                            ${user.imageCount || 0} image${(user.imageCount || 0) !== 1 ? 's' : ''}
                        </span>
                    </td>
                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                    <td>
                        <span class="badge ${activeUsersData.some(au => au.userId === user.id) ? 'badge-success' : 'badge-warning'}">
                            ${activeUsersData.some(au => au.userId === user.id) ? 'Online' : 'Offline'}
                        </span>
                    </td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary btn-sm" onclick="viewUser('${user.id}')" title="View Details">👁️</button>
                            <button class="btn btn-secondary btn-sm" onclick="editUser('${user.id}')" title="Edit User">✏️</button>
                            <button class="btn btn-danger btn-sm" onclick="if(confirm('Are you sure you want to delete this user?')) deleteUser('${user.id}')" title="Delete User">🗑️</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Render matches table
        function renderMatchesTable() {
            const tbody = document.getElementById('matchesTableBody');

            if (matches.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">No matches found</td></tr>';
                return;
            }

            tbody.innerHTML = matches.map(match => `
                <tr>
                    <td><code>${match.id}</code></td>
                    <td>${match.user1Username || match.user1}</td>
                    <td>${match.user2Username || match.user2}</td>
                    <td>${match.distance}km</td>
                    <td>${new Date(match.createdAt).toLocaleString()}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewMatch('${match.id}')">View</button>
                    </td>
                </tr>
            `).join('');
        }

        // Render messages table
        function renderMessagesTable() {
            const tbody = document.getElementById('messagesTableBody');

            if (messages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">No messages found</td></tr>';
                return;
            }

            tbody.innerHTML = messages.map(message => `
                <tr>
                    <td>${message.senderUsername}</td>
                    <td>${message.receiverUsername}</td>
                    <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${message.text}</td>
                    <td>${new Date(message.timestamp).toLocaleString()}</td>
                    <td>
                        <span class="badge ${message.read ? 'badge-success' : 'badge-warning'}">
                            ${message.read ? 'Read' : 'Unread'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewMessage('${message.messageId}')">View</button>
                    </td>
                </tr>
            `).join('');
        }

        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Filter table function
        function filterTable(tableId, searchValue) {
            const table = document.getElementById(tableId);
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].textContent || cells[j].innerText;
                    if (cellText.toLowerCase().indexOf(searchValue.toLowerCase()) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }

        // View user details
        function viewUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            const modalContent = document.getElementById('userModalContent');
            modalContent.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div>
                        <h3>Basic Information</h3>
                        <p><strong>Username:</strong> ${user.username}</p>
                        <p><strong>Email:</strong> ${user.email || 'Not provided'}</p>
                        <p><strong>Age:</strong> ${user.age || 'Not provided'}</p>
                        <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleString()}</p>
                        <p><strong>Status:</strong>
                            <span class="badge ${activeUsersData.some(au => au.userId === user.id) ? 'badge-success' : 'badge-warning'}">
                                ${activeUsersData.some(au => au.userId === user.id) ? 'Online' : 'Offline'}
                            </span>
                        </p>
                    </div>
                    <div>
                        <h3>Profile Details</h3>
                        <p><strong>Description:</strong></p>
                        <p style="background: #f8f9fa; padding: 0.5rem; border-radius: 4px; font-style: italic;">
                            ${user.description || 'No description provided'}
                        </p>
                        <p><strong>Passions:</strong></p>
                        <div class="passions" style="margin-top: 0.5rem;">
                            ${(user.passions || []).map(passion =>
                                `<span class="passion-tag">${passion}</span>`
                            ).join('')}
                        </div>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <h3>Images</h3>
                    <p style="color: #666; margin-top: 0.5rem;">
                        User has ${user.imageCount || 0} image${(user.imageCount || 0) !== 1 ? 's' : ''} uploaded
                        ${(user.imageCount || 0) > 0 ? ' (images not displayed in admin view for performance)' : ''}
                    </p>
                </div>
                <div style="margin-top: 1rem; text-align: right;">
                    <button class="btn btn-danger" onclick="if(confirm('Are you sure you want to delete this user?')) deleteUser('${user.id}')">Delete User</button>
                </div>
            `;

            document.getElementById('userModal').style.display = 'block';
        }

        // Close modal
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Delete user
        async function deleteUser(userId) {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`User ${result.deletedUser.username} deleted successfully`);
                    closeModal('userModal');
                    // Reload the users data
                    await loadUsers();
                    updateStats();
                } else {
                    const error = await response.json();
                    alert(`Error deleting user: ${error.message}`);
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                alert('Error deleting user. Please try again.');
            }
        }

        // View match details (placeholder)
        function viewMatch(matchId) {
            alert(`View match ${matchId} - functionality would be implemented here`);
        }

        // View message details (placeholder)
        function viewMessage(messageId) {
            alert(`View message ${messageId} - functionality would be implemented here`);
        }

        // Show create user modal
        function showCreateUserModal() {
            document.getElementById('userFormTitle').textContent = 'Create New User';
            document.getElementById('userFormSubmit').innerHTML = '<span class="btn-text">Create User</span><div class="spinner" style="display: none;"></div>';
            document.getElementById('userForm').reset();
            document.getElementById('selectedPassions').innerHTML = '';
            selectedPassions = [];
            currentEditingUserId = null;

            // Populate age dropdown
            populateAgeDropdown();

            // Show password field for new users
            document.getElementById('formPassword').parentElement.style.display = 'flex';
            document.getElementById('formPassword').required = true;

            document.getElementById('userFormModal').style.display = 'block';
        }

        // Edit user function
        function editUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            currentEditingUserId = userId;
            document.getElementById('userFormTitle').textContent = 'Edit User';
            document.getElementById('userFormSubmit').innerHTML = '<span class="btn-text">Update User</span><div class="spinner" style="display: none;"></div>';

            // Populate form with user data
            document.getElementById('formUsername').value = user.username || '';
            document.getElementById('formEmail').value = user.email || '';
            document.getElementById('formAge').value = user.age || '';
            document.getElementById('formRole').value = user.role || 'user';
            document.getElementById('formStatus').value = user.accountStatus || 'active';
            document.getElementById('formDescription').value = user.description || '';

            // Hide password field for editing
            document.getElementById('formPassword').parentElement.style.display = 'none';
            document.getElementById('formPassword').required = false;

            // Populate passions
            selectedPassions = [...(user.passions || [])];
            renderSelectedPassions();

            // Populate age dropdown
            populateAgeDropdown();

            document.getElementById('userFormModal').style.display = 'block';
        }

        // Populate age dropdown
        function populateAgeDropdown() {
            const ageSelect = document.getElementById('formAge');
            const currentValue = ageSelect.value;

            ageSelect.innerHTML = '<option value="">Select Age</option>';
            for (let age = 18; age <= 120; age++) {
                const option = document.createElement('option');
                option.value = age;
                option.textContent = age;
                if (age.toString() === currentValue) {
                    option.selected = true;
                }
                ageSelect.appendChild(option);
            }
        }

        // Global variables for form management
        let selectedPassions = [];
        let currentEditingUserId = null;

        // Initialize form handlers
        document.addEventListener('DOMContentLoaded', function() {
            // Passion input handler
            const passionInput = document.getElementById('passionInput');
            passionInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addPassion();
                }
            });

            // User form submit handler
            document.getElementById('userForm').addEventListener('submit', handleUserFormSubmit);
        });

        // Add passion function
        function addPassion() {
            const input = document.getElementById('passionInput');
            const passion = input.value.trim();

            if (passion && !selectedPassions.includes(passion) && selectedPassions.length < 6) {
                selectedPassions.push(passion);
                input.value = '';
                renderSelectedPassions();
            }
        }

        // Remove passion function
        function removePassion(passion) {
            selectedPassions = selectedPassions.filter(p => p !== passion);
            renderSelectedPassions();
        }

        // Render selected passions
        function renderSelectedPassions() {
            const container = document.getElementById('selectedPassions');
            container.innerHTML = selectedPassions.map(passion => `
                <div class="passion-chip">
                    ${passion}
                    <span class="remove" onclick="removePassion('${passion}')">&times;</span>
                </div>
            `).join('');
        }

        // Handle user form submission
        async function handleUserFormSubmit(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                email: formData.get('email') || undefined,
                role: formData.get('role'),
                accountStatus: formData.get('accountStatus'),
                age: formData.get('age') ? parseInt(formData.get('age')) : undefined,
                description: formData.get('description') || '',
                passions: selectedPassions
            };

            // Add password for new users
            if (!currentEditingUserId) {
                userData.password = formData.get('password');
            }

            // Validation
            if (selectedPassions.length < 3) {
                alert('Please select at least 3 passions');
                return;
            }

            if (selectedPassions.length > 6) {
                alert('Please select no more than 6 passions');
                return;
            }

            // Show loading state
            const submitBtn = document.getElementById('userFormSubmit');
            const spinner = submitBtn.querySelector('.spinner');
            const btnText = submitBtn.querySelector('.btn-text');

            submitBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.style.opacity = '0.7';

            try {
                const url = currentEditingUserId
                    ? `${API_BASE}/api/admin/users/${currentEditingUserId}`
                    : `${API_BASE}/api/admin/users`;

                const method = currentEditingUserId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert(currentEditingUserId ? 'User updated successfully!' : 'User created successfully!');
                    closeModal('userFormModal');
                    await loadUsers();
                    updateStats();
                } else {
                    alert(result.error || 'Operation failed');
                }
            } catch (error) {
                console.error('Error saving user:', error);
                alert('Network error. Please try again.');
            } finally {
                // Reset loading state
                submitBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.style.opacity = '1';
            }
        }

        // Bulk actions functions
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');

            if (checkboxes.length > 0) {
                bulkActions.style.display = 'flex';
                selectedCount.textContent = `${checkboxes.length} selected`;
            } else {
                bulkActions.style.display = 'none';
            }
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.user-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkActions();
        }

        function clearSelection() {
            document.getElementById('selectAll').checked = false;
            document.querySelectorAll('.user-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateBulkActions();
        }

        async function executeBulkAction() {
            const action = document.getElementById('bulkActionSelect').value;
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            const userIds = Array.from(checkboxes).map(cb => cb.value);

            if (!action || userIds.length === 0) {
                alert('Please select an action and users');
                return;
            }

            if (!confirm(`Are you sure you want to ${action} ${userIds.length} user(s)?`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ action, userIds })
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`Bulk operation completed successfully`);
                    clearSelection();
                    await loadUsers();
                    updateStats();
                } else {
                    alert(result.error || 'Bulk operation failed');
                }
            } catch (error) {
                console.error('Error executing bulk action:', error);
                alert('Network error. Please try again.');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>