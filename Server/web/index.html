<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shake & Match - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .header-left h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .header-left p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            text-align: right;
        }

        .user-info .username {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .user-info .role {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .header h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            text-align: center;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            padding: 1rem 1.5rem;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .nav-tab:hover {
            background: #f0f0f0;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .search-box {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            width: 250px;
        }

        .table-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .bulk-actions {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .selected-count {
            font-weight: 600;
            color: #667eea;
        }

        .bulk-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            gap: 0.5rem;
        }

        .pagination button {
            padding: 0.5rem 0.75rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-blocked {
            background: #f8d7da;
            color: #721c24;
        }

        .status-deleted {
            background: #d1ecf1;
            color: #0c5460;
        }

        .role-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-admin {
            background: #fff3cd;
            color: #856404;
        }

        .role-user {
            background: #e2e3e5;
            color: #383d41;
        }

        .online-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .online-indicator.online {
            background: #28a745;
        }

        .online-indicator.offline {
            background: #6c757d;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .user-images {
            display: flex;
            gap: 0.5rem;
        }

        .user-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }

        .passions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .passion-tag {
            background: #e9ecef;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            color: #495057;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .search-box {
                width: 200px;
            }

            th, td {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-left">
                    <h1>🤝 Shake & Match</h1>
                    <p>Admin Dashboard</p>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <div class="username" id="currentUsername">Loading...</div>
                        <div class="role" id="currentRole">Admin</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">
                        🚪 Logout
                    </button>
                </div>
            </div>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">-</div>
                <div class="stat-label">Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMatches">-</div>
                <div class="stat-label">Total Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">-</div>
                <div class="stat-label">Messages Sent</div>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('users')">👥 Users</button>
            <button class="nav-tab" onclick="showTab('matches')">💕 Matches</button>
            <button class="nav-tab" onclick="showTab('messages')">💬 Messages</button>
            <button class="nav-tab" onclick="showTab('analytics')">📊 Analytics</button>
        </div>

        <div id="users" class="tab-content active">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">User Management</div>
                    <div class="table-controls">
                        <input type="text" class="search-box" placeholder="Search users..." id="userSearch">
                        <select class="filter-select" id="roleFilter">
                            <option value="">All Roles</option>
                            <option value="user">Users</option>
                            <option value="admin">Admins</option>
                        </select>
                        <select class="filter-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="blocked">Blocked</option>
                            <option value="deleted">Deleted</option>
                        </select>
                        <button class="btn btn-primary" onclick="showCreateUserModal()">➕ Add User</button>
                    </div>
                </div>

                <div class="bulk-actions" id="bulkActions" style="display: none;">
                    <span class="selected-count" id="selectedCount">0 selected</span>
                    <select class="bulk-select" id="bulkActionSelect">
                        <option value="">Bulk Actions</option>
                        <option value="block">Block Selected</option>
                        <option value="unblock">Unblock Selected</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button class="btn btn-warning" onclick="executeBulkAction()">Apply</button>
                    <button class="btn btn-secondary" onclick="clearSelection()">Clear</button>
                </div>

                <div class="table-wrapper">
                    <table id="usersTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortUsers('username')">Username ↕</th>
                                <th onclick="sortUsers('email')">Email ↕</th>
                                <th onclick="sortUsers('role')">Role ↕</th>
                                <th onclick="sortUsers('accountStatus')">Status ↕</th>
                                <th onclick="sortUsers('age')">Age ↕</th>
                                <th>Passions</th>
                                <th>Images</th>
                                <th onclick="sortUsers('createdAt')">Created ↕</th>
                                <th onclick="sortUsers('lastLogin')">Last Login ↕</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr><td colspan="11">Loading users...</td></tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="usersPagination">
                    <!-- Pagination will be inserted here -->
                </div>
            </div>
        </div>

        <div id="matches" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Match History</div>
                    <input type="text" class="search-box" placeholder="Search matches..." id="matchSearch" onkeyup="filterTable('matchesTable', this.value)">
                </div>
                <div class="table-wrapper">
                    <table id="matchesTable">
                        <thead>
                            <tr>
                                <th>Match ID</th>
                                <th>User 1</th>
                                <th>User 2</th>
                                <th>Distance (km)</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="matchesTableBody">
                            <tr><td colspan="6" class="loading">Loading matches...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="messages" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Message History</div>
                    <input type="text" class="search-box" placeholder="Search messages..." id="messageSearch" onkeyup="filterTable('messagesTable', this.value)">
                </div>
                <div class="table-wrapper">
                    <table id="messagesTable">
                        <thead>
                            <tr>
                                <th>From</th>
                                <th>To</th>
                                <th>Message</th>
                                <th>Timestamp</th>
                                <th>Read</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                            <tr><td colspan="6" class="loading">Loading messages...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="analytics" class="tab-content">
            <div class="data-table">
                <div class="table-header">
                    <div class="table-title">Analytics & Insights</div>
                </div>
                <div style="padding: 2rem;">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="avgAge">-</div>
                            <div class="stat-label">Average Age</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="avgDistance">-</div>
                            <div class="stat-label">Avg Match Distance</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="matchRate">-</div>
                            <div class="stat-label">Match Success Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="msgPerMatch">-</div>
                            <div class="stat-label">Messages per Match</div>
                        </div>
                    </div>
                    <div id="analyticsCharts" style="margin-top: 2rem;">
                        <h3>Popular Passions</h3>
                        <div id="passionsChart" style="background: white; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                            Loading analytics...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Detail Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('userModal')">&times;</span>
            <h2>User Details</h2>
            <div id="userModalContent">
                Loading...
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin;

        // Global data storage
        let users = [];
        let matches = [];
        let messages = [];
        let activeUsersData = [];
        let currentUser = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
        });

        // Check authentication status
        async function checkAuthentication() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    // Not authenticated, redirect to login
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update header with user info
                document.getElementById('currentUsername').textContent = currentUser.username;
                document.getElementById('currentRole').textContent = currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);

                // Load dashboard data
                loadDashboardData();
                // Refresh data every 30 seconds
                setInterval(loadDashboardData, 30000);

            } catch (error) {
                console.error('Authentication check failed:', error);
                window.location.href = '/admin/login';
            }
        }

        // Logout function
        async function logout() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/logout`, {
                    method: 'POST',
                    credentials: 'include'
                });

                // Redirect to login regardless of response
                window.location.href = '/admin/login';
            } catch (error) {
                console.error('Logout error:', error);
                // Still redirect to login
                window.location.href = '/admin/login';
            }
        }

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadUsers(),
                    loadMatches(),
                    loadMessages(),
                    loadActiveUsers()
                ]);
                updateStats();
                updateAnalytics();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load users data
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                users = data.users || [];

                // Get detailed user data using admin-specific endpoint
                const detailedUsers = await Promise.all(
                    users.map(async (user) => {
                        try {
                            const profileResponse = await fetch(`${API_BASE}/api/admin/profile/${user.id}`);
                            const profileData = await profileResponse.json();
                            return { ...user, ...profileData.profile };
                        } catch (error) {
                            console.error(`Error loading profile for user ${user.id}:`, error);
                            return user;
                        }
                    })
                );

                users = detailedUsers;
                renderUsersTable();
            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('usersTableBody').innerHTML =
                    '<tr><td colspan="8" class="error">Error loading users</td></tr>';
            }
        }

        // Load matches data
        async function loadMatches() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/matches`, {
                    credentials: 'include'
                });
                const data = await response.json();
                matches = data.matches || [];
                renderMatchesTable();
            } catch (error) {
                console.error('Error loading matches:', error);
                document.getElementById('matchesTableBody').innerHTML =
                    '<tr><td colspan="6" class="error">Error loading matches</td></tr>';
            }
        }

        // Load messages data
        async function loadMessages() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/messages`, {
                    credentials: 'include'
                });
                const data = await response.json();
                messages = data.messages || [];
                renderMessagesTable();
            } catch (error) {
                console.error('Error loading messages:', error);
                document.getElementById('messagesTableBody').innerHTML =
                    '<tr><td colspan="6" class="error">Error loading messages</td></tr>';
            }
        }

        // Load active users
        async function loadActiveUsers() {
            try {
                const response = await fetch(`${API_BASE}/api/debug/active-users`);
                const data = await response.json();
                activeUsersData = data.users || [];
            } catch (error) {
                console.error('Error loading active users:', error);
                activeUsersData = [];
            }
        }

        // Update statistics
        function updateStats() {
            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = activeUsersData.length;
            document.getElementById('totalMatches').textContent = matches.length;
            document.getElementById('totalMessages').textContent = messages.length;
        }

        // Update analytics
        function updateAnalytics() {
            // Calculate average age
            const usersWithAge = users.filter(u => u.age);
            const avgAge = usersWithAge.length > 0
                ? Math.round(usersWithAge.reduce((sum, u) => sum + u.age, 0) / usersWithAge.length)
                : 0;
            document.getElementById('avgAge').textContent = avgAge || '-';

            // Calculate average match distance
            const avgDistance = matches.length > 0
                ? (matches.reduce((sum, m) => sum + m.distance, 0) / matches.length).toFixed(1)
                : 0;
            document.getElementById('avgDistance').textContent = avgDistance ? `${avgDistance}km` : '-';

            // Calculate match rate (placeholder)
            document.getElementById('matchRate').textContent = users.length > 0 ? `${Math.round((matches.length / users.length) * 100)}%` : '-';

            // Calculate messages per match
            const msgPerMatch = matches.length > 0 ? Math.round(messages.length / matches.length) : 0;
            document.getElementById('msgPerMatch').textContent = msgPerMatch || '-';

            // Render popular passions
            renderPopularPassions();
        }

        // Render popular passions chart
        function renderPopularPassions() {
            const passionCounts = {};
            users.forEach(user => {
                if (user.passions && Array.isArray(user.passions)) {
                    user.passions.forEach(passion => {
                        passionCounts[passion] = (passionCounts[passion] || 0) + 1;
                    });
                }
            });

            const sortedPassions = Object.entries(passionCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            const chartHtml = sortedPassions.length > 0
                ? sortedPassions.map(([passion, count]) => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <span>${passion}</span>
                        <span style="background: #667eea; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">${count}</span>
                    </div>
                `).join('')
                : '<p style="text-align: center; color: #666;">No passion data available</p>';

            document.getElementById('passionsChart').innerHTML = chartHtml;
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td><strong>${user.username}</strong></td>
                    <td>${user.email || '-'}</td>
                    <td>${user.age || '-'}</td>
                    <td>
                        <div class="passions">
                            ${(user.passions || []).slice(0, 3).map(passion =>
                                `<span class="passion-tag">${passion}</span>`
                            ).join('')}
                            ${(user.passions || []).length > 3 ? `<span class="passion-tag">+${(user.passions || []).length - 3}</span>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="badge ${(user.imageCount || 0) > 0 ? 'badge-success' : 'badge-secondary'}">
                            ${user.imageCount || 0} image${(user.imageCount || 0) !== 1 ? 's' : ''}
                        </span>
                    </td>
                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                    <td>
                        <span class="badge ${activeUsersData.some(au => au.userId === user.id) ? 'badge-success' : 'badge-warning'}">
                            ${activeUsersData.some(au => au.userId === user.id) ? 'Online' : 'Offline'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewUser('${user.id}')">View</button>
                    </td>
                </tr>
            `).join('');
        }

        // Render matches table
        function renderMatchesTable() {
            const tbody = document.getElementById('matchesTableBody');

            if (matches.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">No matches found</td></tr>';
                return;
            }

            tbody.innerHTML = matches.map(match => `
                <tr>
                    <td><code>${match.id}</code></td>
                    <td>${match.user1Username || match.user1}</td>
                    <td>${match.user2Username || match.user2}</td>
                    <td>${match.distance}km</td>
                    <td>${new Date(match.createdAt).toLocaleString()}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewMatch('${match.id}')">View</button>
                    </td>
                </tr>
            `).join('');
        }

        // Render messages table
        function renderMessagesTable() {
            const tbody = document.getElementById('messagesTableBody');

            if (messages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">No messages found</td></tr>';
                return;
            }

            tbody.innerHTML = messages.map(message => `
                <tr>
                    <td>${message.senderUsername}</td>
                    <td>${message.receiverUsername}</td>
                    <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${message.text}</td>
                    <td>${new Date(message.timestamp).toLocaleString()}</td>
                    <td>
                        <span class="badge ${message.read ? 'badge-success' : 'badge-warning'}">
                            ${message.read ? 'Read' : 'Unread'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewMessage('${message.messageId}')">View</button>
                    </td>
                </tr>
            `).join('');
        }

        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Filter table function
        function filterTable(tableId, searchValue) {
            const table = document.getElementById(tableId);
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].textContent || cells[j].innerText;
                    if (cellText.toLowerCase().indexOf(searchValue.toLowerCase()) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }

        // View user details
        function viewUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            const modalContent = document.getElementById('userModalContent');
            modalContent.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div>
                        <h3>Basic Information</h3>
                        <p><strong>Username:</strong> ${user.username}</p>
                        <p><strong>Email:</strong> ${user.email || 'Not provided'}</p>
                        <p><strong>Age:</strong> ${user.age || 'Not provided'}</p>
                        <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleString()}</p>
                        <p><strong>Status:</strong>
                            <span class="badge ${activeUsersData.some(au => au.userId === user.id) ? 'badge-success' : 'badge-warning'}">
                                ${activeUsersData.some(au => au.userId === user.id) ? 'Online' : 'Offline'}
                            </span>
                        </p>
                    </div>
                    <div>
                        <h3>Profile Details</h3>
                        <p><strong>Description:</strong></p>
                        <p style="background: #f8f9fa; padding: 0.5rem; border-radius: 4px; font-style: italic;">
                            ${user.description || 'No description provided'}
                        </p>
                        <p><strong>Passions:</strong></p>
                        <div class="passions" style="margin-top: 0.5rem;">
                            ${(user.passions || []).map(passion =>
                                `<span class="passion-tag">${passion}</span>`
                            ).join('')}
                        </div>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <h3>Images</h3>
                    <p style="color: #666; margin-top: 0.5rem;">
                        User has ${user.imageCount || 0} image${(user.imageCount || 0) !== 1 ? 's' : ''} uploaded
                        ${(user.imageCount || 0) > 0 ? ' (images not displayed in admin view for performance)' : ''}
                    </p>
                </div>
                <div style="margin-top: 1rem; text-align: right;">
                    <button class="btn btn-danger" onclick="if(confirm('Are you sure you want to delete this user?')) deleteUser('${user.id}')">Delete User</button>
                </div>
            `;

            document.getElementById('userModal').style.display = 'block';
        }

        // Close modal
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Delete user
        async function deleteUser(userId) {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`User ${result.deletedUser.username} deleted successfully`);
                    closeModal('userModal');
                    // Reload the users data
                    await loadUsers();
                    updateStats();
                } else {
                    const error = await response.json();
                    alert(`Error deleting user: ${error.message}`);
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                alert('Error deleting user. Please try again.');
            }
        }

        // View match details (placeholder)
        function viewMatch(matchId) {
            alert(`View match ${matchId} - functionality would be implemented here`);
        }

        // View message details (placeholder)
        function viewMessage(messageId) {
            alert(`View message ${messageId} - functionality would be implemented here`);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>