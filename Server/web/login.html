<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad<PERSON>gin - Shake & Match</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
            z-index: 1;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #4f46e5, #7c3aed);
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo h1 {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-group input.error {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .login-btn {
            width: 100%;
            padding: 1.2rem;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(79, 70, 229, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-btn .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        .login-btn.loading .spinner {
            display: inline-block;
        }

        .login-btn.loading .btn-text {
            opacity: 0.7;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: none;
        }

        .alert.show {
            display: block;
        }

        .alert.error {
            background: #fdf2f2;
            color: #e74c3c;
            border: 1px solid #fadbd8;
        }

        .alert.success {
            background: #f0f9f0;
            color: #27ae60;
            border: 1px solid #d5f4e6;
        }

        .security-info {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.875rem;
            color: #666;
            text-align: center;
        }

        .security-info .icon {
            color: #667eea;
            margin-right: 0.5rem;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .logo h1 {
                font-size: 1.5rem;
            }
        }

        /* Rate limit warning */
        .rate-limit-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: none;
            text-align: center;
        }

        .rate-limit-warning.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🤝 Shake & Match</h1>
            <p>Admin Dashboard</p>
        </div>

        <div id="rateLimitWarning" class="rate-limit-warning">
            <strong>⚠️ Too many login attempts</strong><br>
            Please wait before trying again.
        </div>

        <div id="alert" class="alert">
            <span id="alertMessage"></span>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
                <div class="error-message" id="usernameError"></div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
                <div class="error-message" id="passwordError"></div>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <div class="spinner"></div>
                <span class="btn-text">Sign In</span>
            </button>
        </form>

        <div class="security-info">
            <span class="icon">🔒</span>
            Secure admin access with JWT authentication
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const alert = document.getElementById('alert');
        const alertMessage = document.getElementById('alertMessage');
        const rateLimitWarning = document.getElementById('rateLimitWarning');

        // Check if already authenticated
        checkAuthStatus();

        async function checkAuthStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    // Already authenticated, redirect to dashboard
                    window.location.href = '/admin/';
                }
            } catch (error) {
                // Not authenticated, stay on login page
                console.log('Not authenticated');
            }
        }

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // Clear previous errors
            clearErrors();
            hideAlert();

            // Validate inputs
            if (!validateInputs(username, password)) {
                return;
            }

            // Show loading state
            setLoading(true);

            try {
                const response = await fetch(`${API_BASE}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    showAlert('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 1000);
                } else {
                    if (response.status === 429) {
                        showRateLimitWarning();
                    }
                    showAlert(data.error || 'Login failed', 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Network error. Please try again.', 'error');
            } finally {
                setLoading(false);
            }
        });

        function validateInputs(username, password) {
            let isValid = true;

            if (!username) {
                showFieldError('username', 'Username is required');
                isValid = false;
            } else if (username.length < 3) {
                showFieldError('username', 'Username must be at least 3 characters');
                isValid = false;
            }

            if (!password) {
                showFieldError('password', 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError('password', 'Password must be at least 6 characters');
                isValid = false;
            }

            return isValid;
        }

        function showFieldError(fieldName, message) {
            const field = document.getElementById(fieldName);
            const errorElement = document.getElementById(fieldName + 'Error');
            
            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        function clearErrors() {
            const fields = ['username', 'password'];
            fields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                const errorElement = document.getElementById(fieldName + 'Error');
                
                field.classList.remove('error');
                errorElement.classList.remove('show');
            });
        }

        function showAlert(message, type) {
            alertMessage.textContent = message;
            alert.className = `alert ${type} show`;
        }

        function hideAlert() {
            alert.classList.remove('show');
        }

        function showRateLimitWarning() {
            rateLimitWarning.classList.add('show');
            setTimeout(() => {
                rateLimitWarning.classList.remove('show');
            }, 10000);
        }

        function setLoading(loading) {
            loginBtn.disabled = loading;
            if (loading) {
                loginBtn.classList.add('loading');
            } else {
                loginBtn.classList.remove('loading');
            }
        }

        // Clear rate limit warning when user starts typing
        document.getElementById('username').addEventListener('input', () => {
            rateLimitWarning.classList.remove('show');
        });

        document.getElementById('password').addEventListener('input', () => {
            rateLimitWarning.classList.remove('show');
        });
    </script>
</body>
</html>
