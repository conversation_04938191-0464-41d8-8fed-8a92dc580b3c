#!/bin/bash

# Simple MongoDB Authentication Setup

echo "🔒 Setting up MongoDB Authentication (Simple Method)"
echo "=================================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

# Use simple, known passwords for now
ADMIN_PASSWORD="AdminPass123!"
APP_PASSWORD="AppPass123!"

echo "🔐 Using simple passwords for initial setup"
echo "⚠️  You can change these later for production"

# Make sure MongoDB is running without auth
echo "🛑 Ensuring MongoDB is running without authentication..."
systemctl stop mongod

# Create config without auth
cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOF

systemctl start mongod
sleep 5

if ! systemctl is-active --quiet mongod; then
    echo "❌ MongoDB failed to start"
    systemctl status mongod
    exit 1
fi

echo "✅ MongoDB is running without authentication"

# Create users step by step
echo "👤 Creating admin user..."
mongosh --quiet --eval "
use admin;
db.createUser({
  user: 'admin',
  pwd: '$ADMIN_PASSWORD',
  roles: ['userAdminAnyDatabase', 'readWriteAnyDatabase', 'dbAdminAnyDatabase']
});
print('Admin user created');
"

echo "👤 Creating application user..."
mongosh --quiet --eval "
use shakeAndMatch;
db.createUser({
  user: 'shakeapp',
  pwd: '$APP_PASSWORD',
  roles: [{role: 'readWrite', db: 'shakeAndMatch'}]
});
print('App user created');
"

# Test connections while auth is still disabled
echo "🧪 Testing connections..."
mongosh --quiet "mongodb://127.0.0.1:27017/admin" --eval "print('✅ Basic connection works')"

# Now enable authentication
echo "🔒 Enabling authentication..."
systemctl stop mongod

cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
security:
  authorization: enabled
EOF

systemctl start mongod
sleep 5

if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB started with authentication"
    
    # Test authenticated connections
    echo "🧪 Testing authenticated connections..."
    
    if mongosh --quiet "*****************************************************" --eval "db.runCommand('ping'); print('✅ Admin auth works');" 2>/dev/null; then
        echo "✅ Admin authentication successful"
    else
        echo "❌ Admin authentication failed"
    fi
    
    if mongosh --quiet "**************************************************************" --eval "db.runCommand('ping'); print('✅ App auth works');" 2>/dev/null; then
        echo "✅ Application authentication successful"
        
        echo ""
        echo "🎉 SUCCESS! MongoDB Authentication Enabled"
        echo "========================================="
        echo ""
        echo "📋 Connection Details:"
        echo "Admin: *****************************************************"
        echo "App:   **************************************************************"
        echo ""
        echo "🔄 Update your Node.js server with:"
        echo "**************************************************************"
        echo ""
        echo "✅ MongoDB is now secure and ready!"
        
    else
        echo "❌ Application authentication failed"
        echo "Reverting to no authentication..."
        systemctl stop mongod
        cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOF
        systemctl start mongod
        echo "❌ Reverted to no authentication due to errors"
    fi
else
    echo "❌ MongoDB failed to start with authentication"
    systemctl status mongod
fi
