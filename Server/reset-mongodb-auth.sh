#!/bin/bash

# Reset MongoDB Authentication Script

echo "🔄 Resetting MongoDB Authentication"
echo "==================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

# Stop MongoDB service
echo "🛑 Stopping MongoDB service..."
systemctl stop mongod

# Create MongoDB configuration WITHOUT authentication
echo "📝 Creating MongoDB configuration without authentication..."
cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - No Auth (Temporary)

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - LOCAL ONLY
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - DISABLED for reset
# security:
#   authorization: enabled
EOF

echo "✅ Configuration updated (authentication disabled)"

# Start MongoDB service
echo "🚀 Starting MongoDB without authentication..."
systemctl start mongod

# Wait for MongoDB to start
sleep 5

# Check if MongoDB is running
if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB is running"
    
    # Drop existing users and recreate them
    echo "🗑️  Removing existing users..."
    mongosh --eval "
    use admin;
    try {
      db.dropUser('admin');
      print('✅ Removed admin user');
    } catch(e) {
      print('ℹ️  Admin user did not exist');
    }
    
    use shakeAndMatch;
    try {
      db.dropUser('shakeapp');
      print('✅ Removed shakeapp user');
    } catch(e) {
      print('ℹ️  Shakeapp user did not exist');
    }
    "
    
    # Create fresh users
    echo "👤 Creating fresh MongoDB users..."
    mongosh --eval "
    use admin;
    db.createUser({
      user: 'admin',
      pwd: 'SecurePassword123!',
      roles: [
        { role: 'userAdminAnyDatabase', db: 'admin' },
        { role: 'readWriteAnyDatabase', db: 'admin' },
        { role: 'dbAdminAnyDatabase', db: 'admin' }
      ]
    });
    print('✅ Admin user created successfully');
    
    use shakeAndMatch;
    db.createUser({
      user: 'shakeapp',
      pwd: 'ShakeApp123!',
      roles: [
        { role: 'readWrite', db: 'shakeAndMatch' }
      ]
    });
    print('✅ Application user created successfully');
    "
    
    # Test the users work
    echo "🧪 Testing user authentication..."
    mongosh "mongodb://admin:SecurePassword123!@127.0.0.1:27017/admin" --eval "
    db.runCommand('ping');
    print('✅ Admin user authentication test passed');
    " 2>/dev/null
    
    mongosh "mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch" --eval "
    db.runCommand('ping');
    print('✅ App user authentication test passed');
    " 2>/dev/null
    
    # Now enable authentication
    echo "🔒 Enabling authentication..."
    cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - Secured

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - LOCAL ONLY
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - ENABLED
security:
  authorization: enabled
EOF
    
    # Restart with authentication enabled
    echo "🔄 Restarting MongoDB with authentication enabled..."
    systemctl restart mongod
    sleep 5
    
    if systemctl is-active --quiet mongod; then
        echo "✅ MongoDB restarted with authentication"
        
        # Final test
        echo "🧪 Final authentication test..."
        if mongosh "mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch" --eval "db.runCommand('ping'); print('✅ Final test passed');" 2>/dev/null; then
            echo "🎉 SUCCESS! MongoDB authentication is working"
        else
            echo "❌ Final test failed"
        fi
    else
        echo "❌ MongoDB failed to restart with authentication"
        systemctl status mongod
    fi
    
else
    echo "❌ MongoDB failed to start"
    systemctl status mongod
    exit 1
fi

echo ""
echo "🎉 MongoDB Authentication Reset Complete!"
echo "========================================"
echo ""
echo "📋 Connection Details:"
echo "• Admin: mongodb://admin:SecurePassword123!@127.0.0.1:27017/admin"
echo "• App: mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch"
echo ""
echo "🚀 Your Node.js server should now be able to connect!"
echo "⚠️  Remember to change the default passwords in production!"
