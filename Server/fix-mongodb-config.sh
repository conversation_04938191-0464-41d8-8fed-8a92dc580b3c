#!/bin/bash

# Quick fix for MongoDB configuration error

echo "🔧 Fixing MongoDB configuration..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

# Stop MongoDB service
echo "🛑 Stopping MongoDB service..."
systemctl stop mongod

# Create corrected MongoDB configuration
echo "📝 Creating corrected MongoDB configuration..."
cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - Fixed

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - SECURED
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - DISABLED for initial setup
# security:
#   authorization: enabled
EOF

echo "✅ Configuration fixed"

# Start MongoDB service
echo "🚀 Starting MongoDB service..."
systemctl start mongod

# Wait for MongoDB to start
sleep 3

# Check status
if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB is running successfully"
    
    # Create users without authentication first
    echo "👤 Creating MongoDB users..."
    
    mongosh --eval "
    use admin;
    try {
      db.createUser({
        user: 'admin',
        pwd: 'SecurePassword123!',
        roles: [
          { role: 'userAdminAnyDatabase', db: 'admin' },
          { role: 'readWriteAnyDatabase', db: 'admin' },
          { role: 'dbAdminAnyDatabase', db: 'admin' }
        ]
      });
      print('✅ Admin user created');
    } catch(e) {
      if (e.code === 11000) {
        print('ℹ️  Admin user already exists');
      } else {
        print('❌ Error creating admin user: ' + e.message);
      }
    }
    
    use shakeAndMatch;
    try {
      db.createUser({
        user: 'shakeapp',
        pwd: 'ShakeApp123!',
        roles: [
          { role: 'readWrite', db: 'shakeAndMatch' }
        ]
      });
      print('✅ Application user created');
    } catch(e) {
      if (e.code === 11000) {
        print('ℹ️  Application user already exists');
      } else {
        print('❌ Error creating app user: ' + e.message);
      }
    }
    "
    
    # Now enable authentication
    echo "🔒 Enabling authentication..."
    cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - Secured

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - SECURED
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - ENABLED
security:
  authorization: enabled
EOF
    
    # Restart with authentication
    echo "🔄 Restarting MongoDB with authentication..."
    systemctl restart mongod
    sleep 3
    
    if systemctl is-active --quiet mongod; then
        echo "✅ MongoDB is running with authentication enabled"
        
        # Test connection
        echo "🧪 Testing authenticated connection..."
        mongosh "mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch" --eval "
        db.runCommand('ping');
        print('✅ Authentication test successful');
        " 2>/dev/null && echo "✅ Connection test passed" || echo "⚠️  Connection test failed"
        
    else
        echo "❌ MongoDB failed to start with authentication"
        systemctl status mongod
    fi
    
else
    echo "❌ MongoDB failed to start"
    systemctl status mongod
    exit 1
fi

echo ""
echo "🎉 MongoDB Configuration Fixed!"
echo "================================"
echo ""
echo "📋 Connection Details:"
echo "• Admin: mongodb://admin:SecurePassword123!@127.0.0.1:27017/admin"
echo "• App: mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch"
echo ""
echo "⚠️  Remember to change the default passwords!"
