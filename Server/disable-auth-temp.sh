#!/bin/bash

# Temporarily disable MongoDB authentication to get server running

echo "🔓 Temporarily Disabling MongoDB Authentication"
echo "=============================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "⚠️  WARNING: This will temporarily disable MongoDB authentication"
echo "This is for troubleshooting only. We'll re-enable security later."
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Stop MongoDB service
echo "🛑 Stopping MongoDB service..."
systemctl stop mongod

# Create MongoDB configuration WITHOUT authentication
echo "📝 Creating MongoDB configuration without authentication..."
cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - No Authentication (TEMPORARY)

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - LOCAL ONLY (still secure)
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - DISABLED (TEMPORARY)
# security:
#   authorization: enabled
EOF

echo "✅ Configuration updated (authentication disabled)"

# Start MongoDB service
echo "🚀 Starting MongoDB without authentication..."
systemctl start mongod

# Wait for MongoDB to start
sleep 5

# Check if MongoDB is running
if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB is running without authentication"
    
    # Test connection
    echo "🧪 Testing connection..."
    if mongosh "mongodb://127.0.0.1:27017/shakeAndMatch" --eval "db.runCommand('ping'); print('✅ Connection test passed');" 2>/dev/null; then
        echo "🎉 SUCCESS! MongoDB is accessible without authentication"
        echo ""
        echo "📋 Your Node.js server should now connect successfully!"
        echo "🔗 Connection string: mongodb://127.0.0.1:27017/shakeAndMatch"
        echo ""
        echo "⚠️  IMPORTANT SECURITY NOTES:"
        echo "• Authentication is currently DISABLED"
        echo "• MongoDB only accepts local connections (127.0.0.1)"
        echo "• This is temporary for troubleshooting"
        echo "• We'll re-enable authentication once your app is working"
    else
        echo "❌ Connection test failed"
    fi
else
    echo "❌ MongoDB failed to start"
    systemctl status mongod
    exit 1
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Try running your Node.js server: node server.js"
echo "2. Test your application"
echo "3. Once working, we'll re-enable authentication properly"
