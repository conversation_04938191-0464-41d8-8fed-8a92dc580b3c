#!/bin/bash

# Diagnose MongoDB Authentication Issues

echo "🔍 Diagnosing MongoDB Authentication Issues"
echo "=========================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

# Make sure MongoDB is running without auth
echo "🛑 Ensuring MongoDB is running without authentication..."
systemctl stop mongod

cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOF

systemctl start mongod
sleep 5

if ! systemctl is-active --quiet mongod; then
    echo "❌ MongoDB failed to start"
    exit 1
fi

echo "✅ MongoDB is running"

# Check what databases exist
echo ""
echo "📊 Checking existing databases..."
mongosh --quiet --eval "
db.adminCommand('listDatabases').databases.forEach(function(db) {
  print('Database: ' + db.name);
});
"

# Check what users exist in admin database
echo ""
echo "👥 Checking existing users in admin database..."
mongosh --quiet --eval "
use admin;
try {
  db.getUsers().forEach(function(user) {
    print('Admin user: ' + user.user + ' (roles: ' + JSON.stringify(user.roles) + ')');
  });
} catch(e) {
  print('No users in admin database or error: ' + e.message);
}
"

# Check what users exist in shakeAndMatch database
echo ""
echo "👥 Checking existing users in shakeAndMatch database..."
mongosh --quiet --eval "
use shakeAndMatch;
try {
  db.getUsers().forEach(function(user) {
    print('App user: ' + user.user + ' (roles: ' + JSON.stringify(user.roles) + ')');
  });
} catch(e) {
  print('No users in shakeAndMatch database or error: ' + e.message);
}
"

# Clear all existing users and start fresh
echo ""
echo "🗑️  Clearing all existing users..."
mongosh --quiet --eval "
use admin;
try {
  db.getUsers().forEach(function(user) {
    db.dropUser(user.user);
    print('Dropped admin user: ' + user.user);
  });
} catch(e) {
  print('Error or no admin users to drop: ' + e.message);
}

use shakeAndMatch;
try {
  db.getUsers().forEach(function(user) {
    db.dropUser(user.user);
    print('Dropped app user: ' + user.user);
  });
} catch(e) {
  print('Error or no app users to drop: ' + e.message);
}
"

# Create users with very simple approach
echo ""
echo "👤 Creating users with simple method..."
mongosh --quiet --eval "
use admin;
db.createUser({
  user: 'admin',
  pwd: 'admin123',
  roles: ['root']
});
print('✅ Created admin user with root role');

use shakeAndMatch;
db.createUser({
  user: 'shakeapp',
  pwd: 'shake123',
  roles: ['readWrite']
});
print('✅ Created shakeapp user with readWrite role');
"

# Verify users were created
echo ""
echo "✅ Verifying users were created..."
mongosh --quiet --eval "
use admin;
print('Admin users:');
db.getUsers().forEach(function(user) {
  print('  - ' + user.user);
});

use shakeAndMatch;
print('App users:');
db.getUsers().forEach(function(user) {
  print('  - ' + user.user);
});
"

# Test authentication manually
echo ""
echo "🧪 Testing authentication manually..."

# Test admin user
echo "Testing admin user..."
if mongosh --quiet "**********************************************" --eval "print('Admin auth test passed');" 2>/dev/null; then
    echo "✅ Admin user authentication works"
else
    echo "❌ Admin user authentication failed"
    echo "Checking MongoDB logs for errors..."
    tail -n 10 /var/log/mongodb/mongod.log
fi

# Test app user
echo "Testing app user..."
if mongosh --quiet "*********************************************************" --eval "print('App auth test passed');" 2>/dev/null; then
    echo "✅ App user authentication works"
else
    echo "❌ App user authentication failed"
    echo "Checking MongoDB logs for errors..."
    tail -n 10 /var/log/mongodb/mongod.log
fi

echo ""
echo "🔍 Diagnosis complete. If authentication still fails, there may be a deeper MongoDB configuration issue."
echo ""
echo "📋 If the tests above passed, use these credentials:"
echo "Admin: **********************************************"
echo "App:   *********************************************************"
