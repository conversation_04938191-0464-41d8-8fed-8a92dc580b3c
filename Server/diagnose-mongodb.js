#!/usr/bin/env node

// MongoDB Connection Diagnostic Script
// Run this script on your Ubuntu VPS to diagnose MongoDB connection issues

const mongoose = require('mongoose');

console.log('=== MongoDB Connection Diagnostic Tool ===\n');

// Test different connection scenarios
const testConnections = [
  {
    name: 'Local MongoDB (localhost)',
    uri: 'mongodb://localhost:27017/shakeAndMatch',
    description: 'Testing connection to local MongoDB instance'
  },
  {
    name: 'Local MongoDB with 127.0.0.1',
    uri: 'mongodb://127.0.0.1:27017/shakeAndMatch',
    description: 'Testing connection using 127.0.0.1 instead of localhost'
  },
  {
    name: 'External IP MongoDB',
    uri: 'mongodb://**************:27017/shakeAndMatch',
    description: 'Testing connection to MongoDB using external IP'
  }
];

async function testConnection(config) {
  console.log(`\n--- Testing: ${config.name} ---`);
  console.log(`Description: ${config.description}`);
  console.log(`URI: ${config.uri}`);
  
  const options = {
    serverSelectionTimeoutMS: 5000, // 5 second timeout for quick testing
    socketTimeoutMS: 5000,
    connectTimeoutMS: 5000,
    maxPoolSize: 10,
    minPoolSize: 1,
  };

  try {
    console.log('Attempting connection...');
    const connection = await mongoose.createConnection(config.uri, options);
    
    console.log('✅ Connection successful!');
    console.log(`Connection state: ${connection.readyState}`);

    // Test a simple operation
    try {
      if (connection.db) {
        console.log(`Database name: ${connection.db.databaseName}`);
        const collections = await connection.db.listCollections().toArray();
        console.log(`Collections found: ${collections.length}`);
        collections.forEach(col => console.log(`  - ${col.name}`));
      } else {
        console.log('Database object not available');
      }
    } catch (listError) {
      console.log('⚠️  Could not list collections:', listError.message);
    }
    
    await connection.close();
    console.log('Connection closed successfully');
    
  } catch (error) {
    console.log('❌ Connection failed');
    console.log(`Error type: ${error.name}`);
    console.log(`Error message: ${error.message}`);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Suggestion: MongoDB service may not be running');
    } else if (error.message.includes('timeout')) {
      console.log('💡 Suggestion: Check firewall settings and network connectivity');
    } else if (error.message.includes('authentication')) {
      console.log('💡 Suggestion: Check MongoDB authentication settings');
    }
  }
}

async function checkSystemInfo() {
  console.log('\n=== System Information ===');
  
  // Check if MongoDB is installed and running
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);
  
  try {
    console.log('\n--- MongoDB Service Status ---');
    const { stdout: serviceStatus } = await execAsync('systemctl status mongod || systemctl status mongodb || echo "MongoDB service not found"');
    console.log(serviceStatus);
  } catch (error) {
    console.log('Could not check MongoDB service status');
  }
  
  try {
    console.log('\n--- MongoDB Process ---');
    const { stdout: processInfo } = await execAsync('ps aux | grep mongod | grep -v grep || echo "No MongoDB process found"');
    console.log(processInfo);
  } catch (error) {
    console.log('Could not check MongoDB processes');
  }
  
  try {
    console.log('\n--- Network Ports ---');
    const { stdout: portInfo } = await execAsync('netstat -tlnp | grep :27017 || ss -tlnp | grep :27017 || echo "Port 27017 not listening"');
    console.log(portInfo);
  } catch (error) {
    console.log('Could not check port 27017 status');
  }
  
  try {
    console.log('\n--- Firewall Status ---');
    const { stdout: firewallInfo } = await execAsync('ufw status || iptables -L | head -20 || echo "Could not check firewall"');
    console.log(firewallInfo);
  } catch (error) {
    console.log('Could not check firewall status');
  }
}

async function runDiagnostics() {
  try {
    await checkSystemInfo();
    
    console.log('\n=== Connection Tests ===');
    
    for (const config of testConnections) {
      await testConnection(config);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n=== Diagnostic Complete ===');
    console.log('\nNext steps based on results:');
    console.log('1. If no connections work: Install and start MongoDB');
    console.log('2. If only localhost works: Configure MongoDB for external connections');
    console.log('3. If connections timeout: Check firewall settings');
    console.log('4. If authentication fails: Check MongoDB user permissions');
    
  } catch (error) {
    console.error('Diagnostic script error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the diagnostics
runDiagnostics();
