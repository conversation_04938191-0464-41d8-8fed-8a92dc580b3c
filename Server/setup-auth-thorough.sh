#!/bin/bash

# Thorough MongoDB Authentication Setup

echo "🔐 MongoDB Authentication Setup (Thorough Method)"
echo "================================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "🔑 This will set up MongoDB with proper username/password authentication"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Step 1: Stop MongoDB and clear any existing auth issues
echo "🛑 Step 1: Stopping MongoDB and clearing auth state..."
systemctl stop mongod
sleep 2

# Remove any lock files that might cause issues
rm -f /var/lib/mongodb/mongod.lock

# Step 2: Start MongoDB without authentication
echo "🚀 Step 2: Starting MongoDB without authentication..."
cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOF

systemctl start mongod
sleep 5

# Verify MongoDB is running
if ! systemctl is-active --quiet mongod; then
    echo "❌ MongoDB failed to start. Checking logs..."
    tail -n 20 /var/log/mongodb/mongod.log
    exit 1
fi

echo "✅ MongoDB is running without authentication"

# Step 3: Clear any existing users completely
echo "🗑️  Step 3: Clearing any existing users..."
mongosh --quiet --eval "
// Clear admin database users
use admin;
try {
  db.getUsers().forEach(function(user) {
    db.dropUser(user.user);
    print('Dropped admin user: ' + user.user);
  });
} catch(e) {
  print('No admin users to drop or error: ' + e.message);
}

// Clear application database users
use shakeAndMatch;
try {
  db.getUsers().forEach(function(user) {
    db.dropUser(user.user);
    print('Dropped app user: ' + user.user);
  });
} catch(e) {
  print('No app users to drop or error: ' + e.message);
}

// Also clear the system.users collection if it exists
use admin;
try {
  db.system.users.deleteMany({});
  print('Cleared system.users collection');
} catch(e) {
  print('system.users collection handling: ' + e.message);
}
"

# Step 4: Create users with explicit database context
echo "👤 Step 4: Creating users with explicit database context..."

# Create admin user first
echo "Creating admin user..."
mongosh --quiet --eval "
use admin;
var result = db.createUser({
  user: 'admin',
  pwd: 'SecureAdmin123!',
  roles: [
    { role: 'userAdminAnyDatabase', db: 'admin' },
    { role: 'readWriteAnyDatabase', db: 'admin' },
    { role: 'dbAdminAnyDatabase', db: 'admin' }
  ]
});
print('Admin user creation result: ' + JSON.stringify(result));
"

# Verify admin user was created
echo "Verifying admin user..."
mongosh --quiet --eval "
use admin;
var users = db.getUsers();
if (users.length > 0) {
  print('✅ Admin user verified: ' + users[0].user);
} else {
  print('❌ No admin user found');
}
"

# Create application user
echo "Creating application user..."
mongosh --quiet --eval "
use shakeAndMatch;
var result = db.createUser({
  user: 'shakeapp',
  pwd: 'ShakeApp123!',
  roles: [
    { role: 'readWrite', db: 'shakeAndMatch' }
  ]
});
print('App user creation result: ' + JSON.stringify(result));
"

# Verify app user was created
echo "Verifying app user..."
mongosh --quiet --eval "
use shakeAndMatch;
var users = db.getUsers();
if (users.length > 0) {
  print('✅ App user verified: ' + users[0].user);
} else {
  print('❌ No app user found');
}
"

# Step 5: Test authentication while still in no-auth mode
echo "🧪 Step 5: Testing user creation (still in no-auth mode)..."

# Test by switching to the user's database and checking
mongosh --quiet --eval "
use admin;
var adminUsers = db.getUsers();
use shakeAndMatch;
var appUsers = db.getUsers();

print('Admin database users: ' + adminUsers.length);
adminUsers.forEach(function(user) {
  print('  - ' + user.user + ' (roles: ' + user.roles.length + ')');
});

print('ShakeAndMatch database users: ' + appUsers.length);
appUsers.forEach(function(user) {
  print('  - ' + user.user + ' (roles: ' + user.roles.length + ')');
});

if (adminUsers.length > 0 && appUsers.length > 0) {
  print('✅ Both users exist - ready for authentication');
} else {
  print('❌ Missing users - authentication will fail');
}
"

# Step 6: Enable authentication
echo "🔒 Step 6: Enabling authentication..."
systemctl stop mongod
sleep 2

cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
security:
  authorization: enabled
EOF

systemctl start mongod
sleep 5

if ! systemctl is-active --quiet mongod; then
    echo "❌ MongoDB failed to start with authentication"
    echo "Checking logs..."
    tail -n 20 /var/log/mongodb/mongod.log
    exit 1
fi

echo "✅ MongoDB started with authentication enabled"

# Step 7: Test authentication
echo "🧪 Step 7: Testing authentication..."

echo "Testing admin user authentication..."
if mongosh --quiet "mongodb://admin:SecureAdmin123!@127.0.0.1:27017/admin" --eval "
db.runCommand('ping');
print('✅ Admin authentication successful');
" 2>/dev/null; then
    echo "✅ Admin user authentication works!"
else
    echo "❌ Admin user authentication failed"
    echo "MongoDB logs:"
    tail -n 10 /var/log/mongodb/mongod.log | grep -i auth
fi

echo "Testing app user authentication..."
if mongosh --quiet "mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch" --eval "
db.runCommand('ping');
print('✅ App authentication successful');
" 2>/dev/null; then
    echo "✅ App user authentication works!"
    
    echo ""
    echo "🎉 SUCCESS! MongoDB Authentication is Working!"
    echo "============================================="
    echo ""
    echo "📋 Connection Details:"
    echo "Admin: mongodb://admin:SecureAdmin123!@127.0.0.1:27017/admin"
    echo "App:   mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch"
    echo ""
    echo "🔄 Update your Node.js server with:"
    echo "mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch"
    echo ""
    echo "⚠️  IMPORTANT: Change these passwords in production!"
    
else
    echo "❌ App user authentication failed"
    echo "MongoDB logs:"
    tail -n 10 /var/log/mongodb/mongod.log | grep -i auth
    
    echo ""
    echo "❌ Authentication setup failed. Reverting to no authentication..."
    systemctl stop mongod
    cat > /etc/mongod.conf << 'EOF'
storage:
  dbPath: /var/lib/mongodb
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOF
    systemctl start mongod
    echo "Reverted to working configuration without authentication"
fi
