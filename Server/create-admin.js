// create-admin.js - <PERSON>ript to create an admin user
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const User = require('./models/User');

async function createAdmin() {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch';
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Admin user details
    const adminUsername = process.argv[2] || 'admin';
    const adminPassword = process.argv[3] || 'admin123';
    const adminEmail = process.argv[4] || '<EMAIL>';

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ username: adminUsername });
    if (existingAdmin) {
      console.log(`❌ Admin user '${adminUsername}' already exists`);
      
      // Update existing user to admin role if not already
      if (existingAdmin.role !== 'admin') {
        existingAdmin.role = 'admin';
        existingAdmin.accountStatus = 'active';
        await existingAdmin.save();
        console.log(`✅ Updated existing user '${adminUsername}' to admin role`);
      }
      
      process.exit(0);
    }

    // Hash the password
    console.log('Hashing password...');
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // Create the admin user
    console.log('Creating admin user...');
    const adminUser = new User({
      username: adminUsername,
      password: hashedPassword,
      email: adminEmail,
      role: 'admin',
      accountStatus: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await adminUser.save();

    console.log('✅ Admin user created successfully!');
    console.log('==========================================');
    console.log(`Username: ${adminUsername}`);
    console.log(`Password: ${adminPassword}`);
    console.log(`Email: ${adminEmail}`);
    console.log(`Role: admin`);
    console.log('==========================================');
    console.log('You can now login to the admin dashboard at: /admin/login');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Check command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node create-admin.js [username] [password] [email]');
  console.log('');
  console.log('Examples:');
  console.log('  node create-admin.js');
  console.log('  node create-admin.js admin admin123');
  console.log('  node create-admin.js <NAME_EMAIL>');
  console.log('');
  console.log('Default values:');
  console.log('  username: admin');
  console.log('  password: admin123');
  console.log('  email: <EMAIL>');
  process.exit(0);
}

// Run the script
createAdmin();
