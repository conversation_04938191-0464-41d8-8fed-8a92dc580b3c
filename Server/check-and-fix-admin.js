// check-and-fix-admin.js - Debug and fix admin user
const mongoose = require('mongoose');
const User = require('./models/User');

async function checkAndFixAdmin() {
  try {
    // Connect to MongoDB using the same connection string as server
    const mongoURI = process.env.MONGODB_URI || 'mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch';
    console.log('Connecting to MongoDB...');
    console.log('Connection string:', mongoURI.replace(/:[^:@]*@/, ':****@')); // Hide password
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Get username from command line or use default
    const username = process.argv[2] || 'admin';
    console.log(`\n🔍 Looking for user: ${username}`);

    // Find the user and show all details
    const user = await User.findOne({ username: username });
    if (!user) {
      console.log(`❌ User '${username}' not found`);
      console.log('\n📋 All users in database:');
      const allUsers = await User.find({}).select('username role accountStatus email createdAt');
      if (allUsers.length === 0) {
        console.log('  No users found in database');
      } else {
        allUsers.forEach(u => {
          console.log(`  - Username: ${u.username}`);
          console.log(`    Role: ${u.role || 'undefined'}`);
          console.log(`    Status: ${u.accountStatus || 'undefined'}`);
          console.log(`    Email: ${u.email || 'not set'}`);
          console.log(`    Created: ${u.createdAt}`);
          console.log('    ---');
        });
      }
      process.exit(1);
    }

    console.log('\n📊 Current user data:');
    console.log(`  Username: ${user.username}`);
    console.log(`  Role: ${user.role || 'undefined'}`);
    console.log(`  Account Status: ${user.accountStatus || 'undefined'}`);
    console.log(`  Email: ${user.email || 'not set'}`);
    console.log(`  Login Attempts: ${user.loginAttempts || 0}`);
    console.log(`  Lock Until: ${user.lockUntil || 'not locked'}`);
    console.log(`  Last Login: ${user.lastLogin || 'never'}`);
    console.log(`  Created: ${user.createdAt}`);
    console.log(`  Updated: ${user.updatedAt}`);

    // Try multiple update methods
    console.log('\n🔧 Method 1: Using Mongoose document save...');
    try {
      user.role = 'admin';
      user.accountStatus = 'active';
      user.updatedAt = new Date();
      user.loginAttempts = 0;
      user.lockUntil = undefined;

      const saveResult = await user.save();
      console.log('Save result successful:', !!saveResult);
    } catch (saveError) {
      console.log('Save method failed:', saveError.message);
    }

    console.log('\n🔧 Method 2: Using findByIdAndUpdate...');
    try {
      const updateResult = await User.findByIdAndUpdate(
        user._id,
        {
          role: 'admin',
          accountStatus: 'active',
          updatedAt: new Date(),
          loginAttempts: 0,
          $unset: { lockUntil: 1 }
        },
        { new: true, runValidators: false }
      );
      console.log('findByIdAndUpdate result:', !!updateResult);
    } catch (updateError) {
      console.log('findByIdAndUpdate failed:', updateError.message);
    }

    console.log('\n🔧 Method 3: Using direct collection update...');
    try {
      const collection = mongoose.connection.db.collection('users');
      const directResult = await collection.updateOne(
        { username: username },
        {
          $set: {
            role: 'admin',
            accountStatus: 'active',
            updatedAt: new Date(),
            loginAttempts: 0
          },
          $unset: { lockUntil: 1 }
        }
      );
      console.log('Direct collection update result:', directResult);
    } catch (directError) {
      console.log('Direct collection update failed:', directError.message);
    }

    // Verify the update
    console.log('\n🔍 Checking final result...');
    const updatedUser = await User.findOne({ username: username });
    console.log('\n✅ Final user data:');
    console.log(`  Username: ${updatedUser.username}`);
    console.log(`  Role: ${updatedUser.role || 'still undefined'}`);
    console.log(`  Account Status: ${updatedUser.accountStatus || 'still undefined'}`);
    console.log(`  Email: ${updatedUser.email || 'not set'}`);
    console.log(`  Login Attempts: ${updatedUser.loginAttempts || 0}`);
    console.log(`  Lock Until: ${updatedUser.lockUntil || 'not locked'}`);
    console.log(`  Updated: ${updatedUser.updatedAt || 'still undefined'}`);

    if (updatedUser.role === 'admin' && updatedUser.accountStatus === 'active') {
      console.log('\n🎉 SUCCESS! User is now properly configured as admin.');
      console.log('You can now login at: /admin/login');
      console.log(`Credentials: ${username} / [your password]`);
    } else {
      console.log('\n❌ Update still failed. This might be a schema or database issue.');
      console.log('Let\'s try creating a completely new admin user...');

      // Create a new admin user as fallback
      try {
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash('admin123', 10);

        const newAdmin = new User({
          username: 'newadmin',
          password: hashedPassword,
          email: '<EMAIL>',
          role: 'admin',
          accountStatus: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        });

        await newAdmin.save();
        console.log('\n✅ Created new admin user:');
        console.log('Username: newadmin');
        console.log('Password: admin123');
        console.log('You can login with these credentials at /admin/login');

      } catch (createError) {
        console.log('Failed to create new admin user:', createError.message);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
checkAndFixAdmin();
