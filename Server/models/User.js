// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // MongoDB automatically creates _id field
  // We don't need to manually define it
  
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  email: {
    type: String,
    sparse: true, // Allow multiple null values but unique non-null values
    lowercase: true,
    trim: true
  },
  socialAuth: {
    google: {
      id: String,
      email: String,
      name: String,
      photo: String
    },
    apple: {
      id: String,
      email: String,
      name: String
    },
    facebook: {
      id: String,
      email: String,
      name: String,
      photo: String
    }
  },
  age: {
    type: Number,
    min: 18,
    max: 120
  },
  description: {
    type: String,
    maxlength: 500,
    default: ""
  },
  passions: {
    type: [String],
    default: []
  },
  images: {
    type: [String], // Array of image URLs or base64 encoded images
    default: [],
    validate: [arrayLimit, '{PATH} exceeds the limit of 4 images']
  },
  // Admin and account management fields
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  accountStatus: {
    type: String,
    enum: ['active', 'blocked', 'deleted'],
    default: 'active'
  },
  lastLogin: {
    type: Date,
    default: null
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Add virtual getter for id that returns _id as string
userSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// Virtual field to check if account is locked
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Virtual field to check if user is online (based on lastLogin within last 5 minutes)
userSchema.virtual('isOnline').get(function() {
  if (!this.lastLogin) return false;
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.lastLogin > fiveMinutesAgo;
});

// Ensure virtual fields are included when converting to JSON
userSchema.set('toJSON', {
  virtuals: true,
  transform: (doc, ret) => {
    ret.id = ret._id.toString();
    // Don't include sensitive fields in JSON output
    delete ret.password;
    delete ret.loginAttempts;
    delete ret.lockUntil;
    return ret;
  }
});

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }

  const updates = { $inc: { loginAttempts: 1 } };

  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }

  return this.updateOne(updates);
};

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLogin: new Date() }
  });
};

// Validator to limit array length to 4
function arrayLimit(val) {
  return val.length <= 4;
}

module.exports = mongoose.model('User', userSchema);