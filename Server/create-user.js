#!/usr/bin/env node

// <PERSON>ript to create a user in the MongoDB database
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const User = require('./models/User');

async function createUser(username, password) {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://**************:27017/shakeAndMatch';
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Check if user already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      console.log(`❌ User '${username}' already exists`);
      process.exit(1);
    }

    // Hash the password
    console.log('Hashing password...');
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the user
    console.log('Creating user...');
    const newUser = new User({
      username,
      password: hashedPassword,
      createdAt: new Date()
    });

    await newUser.save();
    console.log(`✅ User '${username}' created successfully`);
    console.log(`User ID: ${newUser._id}`);

    // Verify the user was created
    const verifyUser = await User.findOne({ username });
    if (verifyUser) {
      console.log('✅ User verification successful');
      console.log(`Username: ${verifyUser.username}`);
      console.log(`Created: ${verifyUser.createdAt}`);
    }

  } catch (error) {
    console.error('❌ Error creating user:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 2) {
  console.log('Usage: node create-user.js <username> <password>');
  console.log('Example: node create-user.js mario54 newnew54');
  process.exit(1);
}

const [username, password] = args;

console.log('=== User Creation Script ===');
console.log(`Creating user: ${username}`);
console.log('');

createUser(username, password);
