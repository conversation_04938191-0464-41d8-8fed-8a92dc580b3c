#!/bin/bash

# Fix MongoDB startup issues

echo "🔧 Fixing MongoDB Startup Issues"
echo "================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "🔍 Diagnosing MongoDB startup issues..."

# Check MongoDB service status
echo "📊 Current MongoDB service status:"
systemctl status mongod --no-pager -l

# Check if MongoDB process is running
echo ""
echo "🔍 Checking for MongoDB processes:"
ps aux | grep mongod | grep -v grep || echo "No MongoDB processes found"

# Check MongoDB logs for errors
echo ""
echo "📋 Recent MongoDB logs:"
tail -n 20 /var/log/mongodb/mongod.log

# Check file permissions
echo ""
echo "🔐 Checking file permissions:"
ls -la /var/lib/mongodb/
ls -la /var/log/mongodb/

# Fix common issues
echo ""
echo "🛠️  Fixing common MongoDB startup issues..."

# Stop any running MongoDB processes
echo "🛑 Stopping any running MongoDB processes..."
systemctl stop mongod
pkill -f mongod || true
sleep 3

# Remove lock file if it exists
echo "🗑️  Removing lock files..."
rm -f /var/lib/mongodb/mongod.lock
rm -f /tmp/mongodb-*.sock

# Fix ownership and permissions
echo "🔐 Fixing ownership and permissions..."
chown -R mongodb:mongodb /var/lib/mongodb
chown -R mongodb:mongodb /var/log/mongodb
chmod 755 /var/lib/mongodb
chmod 644 /var/log/mongodb/mongod.log

# Create a minimal working configuration
echo "📝 Creating minimal MongoDB configuration..."
cat > /etc/mongod.conf << 'EOF'
# Minimal MongoDB Configuration
storage:
  dbPath: /var/lib/mongodb

systemLog:
  destination: file
  path: /var/log/mongodb/mongod.log
  logAppend: true

net:
  bindIp: 127.0.0.1
  port: 27017

processManagement:
  fork: true
  pidFilePath: /var/run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo
EOF

# Ensure PID directory exists
echo "📁 Creating PID directory..."
mkdir -p /var/run/mongodb
chown mongodb:mongodb /var/run/mongodb

# Try to start MongoDB
echo "🚀 Attempting to start MongoDB..."
systemctl start mongod
sleep 5

# Check if it started successfully
if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB started successfully!"
    
    # Test connection
    echo "🧪 Testing connection..."
    if mongosh "mongodb://127.0.0.1:27017/test" --eval "db.runCommand('ping'); print('✅ Connection successful');" 2>/dev/null; then
        echo "✅ MongoDB is working perfectly!"
        echo ""
        echo "📋 MongoDB Status:"
        systemctl status mongod --no-pager -l
        echo ""
        echo "🎉 Your Node.js server should now be able to connect!"
        echo "🔗 Connection string: mongodb://127.0.0.1:27017/shakeAndMatch"
    else
        echo "❌ Connection test failed"
    fi
else
    echo "❌ MongoDB failed to start"
    echo ""
    echo "📋 Service status:"
    systemctl status mongod --no-pager -l
    echo ""
    echo "📋 Recent logs:"
    tail -n 30 /var/log/mongodb/mongod.log
    echo ""
    echo "🔧 Trying alternative startup method..."
    
    # Try starting MongoDB directly
    echo "🚀 Trying direct MongoDB startup..."
    sudo -u mongodb mongod --config /etc/mongod.conf &
    sleep 5
    
    if pgrep mongod > /dev/null; then
        echo "✅ MongoDB started with direct method"
        echo "🧪 Testing connection..."
        if mongosh "mongodb://127.0.0.1:27017/test" --eval "db.runCommand('ping');" 2>/dev/null; then
            echo "✅ Direct startup successful!"
        else
            echo "❌ Direct startup connection failed"
        fi
    else
        echo "❌ Direct startup also failed"
        echo ""
        echo "🆘 MongoDB startup troubleshooting needed"
        echo "Please check:"
        echo "1. Disk space: df -h"
        echo "2. MongoDB version: mongod --version"
        echo "3. System resources: free -h"
    fi
fi
