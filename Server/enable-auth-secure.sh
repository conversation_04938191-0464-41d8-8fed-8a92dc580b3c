#!/bin/bash

# Re-enable MongoDB Authentication Securely

echo "🔒 Re-enabling MongoDB Authentication"
echo "===================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "🔐 This will re-enable MongoDB authentication with proper security"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Check if MongoDB is running
if ! systemctl is-active --quiet mongod; then
    echo "❌ MongoDB is not running. Starting it first..."
    systemctl start mongod
    sleep 3
fi

# Create users while authentication is still disabled
echo "👤 Creating MongoDB users..."

# Generate secure random passwords
ADMIN_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
APP_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

echo "🔑 Generated secure passwords"

# Create admin user
mongosh --eval "
use admin;
try {
  db.dropUser('admin');
  print('🗑️  Removed existing admin user');
} catch(e) {
  print('ℹ️  No existing admin user to remove');
}

db.createUser({
  user: 'admin',
  pwd: '$ADMIN_PASSWORD',
  roles: [
    { role: 'userAdminAnyDatabase', db: 'admin' },
    { role: 'readWriteAnyDatabase', db: 'admin' },
    { role: 'dbAdminAnyDatabase', db: 'admin' }
  ]
});
print('✅ Admin user created with secure password');
"

# Create application user
mongosh --eval "
use shakeAndMatch;
try {
  db.dropUser('shakeapp');
  print('🗑️  Removed existing app user');
} catch(e) {
  print('ℹ️  No existing app user to remove');
}

db.createUser({
  user: 'shakeapp',
  pwd: '$APP_PASSWORD',
  roles: [
    { role: 'readWrite', db: 'shakeAndMatch' }
  ]
});
print('✅ Application user created with secure password');
"

# Test users work before enabling auth
echo "🧪 Testing users before enabling authentication..."
if mongosh "*****************************************************" --eval "db.runCommand('ping');" &>/dev/null; then
    echo "✅ Admin user test passed"
else
    echo "❌ Admin user test failed"
    exit 1
fi

if mongosh "**************************************************************" --eval "db.runCommand('ping');" &>/dev/null; then
    echo "✅ App user test passed"
else
    echo "❌ App user test failed"
    exit 1
fi

# Now enable authentication
echo "🔒 Enabling authentication..."
systemctl stop mongod

# Create secure MongoDB configuration
cat > /etc/mongod.conf << 'EOF'
# MongoDB Configuration File - Secured

# Where to store data
storage:
  dbPath: /var/lib/mongodb

# Where to write logging data
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

# Network interfaces - LOCAL ONLY
net:
  port: 27017
  bindIp: 127.0.0.1

# Process management
processManagement:
  timeZoneInfo: /usr/share/zoneinfo

# Security settings - ENABLED
security:
  authorization: enabled
EOF

# Start MongoDB with authentication
echo "🚀 Starting MongoDB with authentication enabled..."
systemctl start mongod
sleep 5

if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB started with authentication"
    
    # Final test
    echo "🧪 Final authentication test..."
    if mongosh "**************************************************************" --eval "db.runCommand('ping');" &>/dev/null; then
        echo "✅ Authentication is working perfectly!"
        
        # Save credentials to file
        cat > /root/mongodb-credentials.txt << EOF
# MongoDB Credentials - KEEP SECURE!
# Generated: $(date)

Admin User:
*****************************************************

Application User:
**************************************************************

# Update your Node.js server with the application connection string above
EOF
        
        chmod 600 /root/mongodb-credentials.txt
        
        echo ""
        echo "🎉 MongoDB Security Successfully Enabled!"
        echo "========================================"
        echo ""
        echo "📋 IMPORTANT - Update Your Application:"
        echo "Update your Node.js server connection string to:"
        echo "**************************************************************"
        echo ""
        echo "🔐 Credentials saved to: /root/mongodb-credentials.txt"
        echo "⚠️  Keep these credentials secure!"
        echo ""
        echo "🔄 Next step: Update your server.js file with the new connection string"
        
    else
        echo "❌ Final authentication test failed"
        exit 1
    fi
else
    echo "❌ MongoDB failed to start with authentication"
    systemctl status mongod
    exit 1
fi
