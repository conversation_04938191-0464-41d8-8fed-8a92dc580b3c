// Load Testing Configuration
const config = {
  // Server configuration
  server: {
    host: process.env.SERVER_HOST || '**************',
    port: process.env.SERVER_PORT || 3000,
    protocol: 'http'
  },

  // Test configuration
  test: {
    totalUsers: parseInt(process.env.TOTAL_USERS) || 50000,
    rampUpTime: parseInt(process.env.RAMP_UP_TIME) || 300, // 5 minutes
    testDuration: parseInt(process.env.TEST_DURATION) || 600, // 10 minutes
    batchSize: parseInt(process.env.BATCH_SIZE) || 100, // Users per batch
    batchDelay: parseInt(process.env.BATCH_DELAY) || 1000, // ms between batches
  },

  // Database configuration
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://**************:27017/shakeAndMatch',
    testDbName: 'shakeAndMatch_loadtest'
  },

  // Test scenarios configuration
  scenarios: {
    registration: {
      enabled: true,
      percentage: 20, // 20% of users will register
      concurrent: 1000
    },
    profileUpdate: {
      enabled: true,
      percentage: 60, // 60% of users will update profiles
      concurrent: 2000,
      imageUploadChance: 0.7, // 70% chance to upload images
      maxImages: 4
    },
    socketConnection: {
      enabled: true,
      percentage: 90, // 90% of users will connect via socket
      concurrent: 5000,
      keepAliveTime: 300000 // 5 minutes
    },
    matching: {
      enabled: true,
      percentage: 80, // 80% of users will shake for matches
      concurrent: 3000,
      shakeInterval: 10000, // Shake every 10 seconds
      locationRadius: 10 // 10km radius for location generation
    },
    messaging: {
      enabled: true,
      percentage: 40, // 40% of users will send messages
      concurrent: 1500,
      messagesPerUser: 5,
      messageInterval: 5000 // Send message every 5 seconds
    }
  },

  // Performance thresholds
  thresholds: {
    responseTime: {
      p95: 2000, // 95th percentile should be under 2s
      p99: 5000  // 99th percentile should be under 5s
    },
    errorRate: 0.05, // Max 5% error rate
    throughput: {
      registration: 100, // registrations per second
      profileUpdate: 200, // profile updates per second
      socketConnection: 500, // socket connections per second
      matching: 300, // matches per second
      messaging: 400 // messages per second
    }
  },

  // Monitoring configuration
  monitoring: {
    enabled: true,
    interval: 5000, // Monitor every 5 seconds
    metrics: [
      'cpu',
      'memory',
      'network',
      'database',
      'socketConnections',
      'activeUsers'
    ]
  },

  // Reporting configuration
  reporting: {
    enabled: true,
    outputDir: './load-tests/reports',
    formats: ['json', 'html', 'csv'],
    realTimeUpdates: true
  }
};

// Helper functions
config.getServerUrl = () => {
  return `${config.server.protocol}://${config.server.host}:${config.server.port}`;
};

config.getApiUrl = (endpoint) => {
  return `${config.getServerUrl()}/api${endpoint}`;
};

config.getSocketUrl = () => {
  return config.getServerUrl();
};

// Validation
config.validate = () => {
  const errors = [];
  
  if (!config.server.host) {
    errors.push('Server host is required');
  }
  
  if (!config.server.port) {
    errors.push('Server port is required');
  }
  
  if (config.test.totalUsers <= 0) {
    errors.push('Total users must be greater than 0');
  }
  
  if (config.test.batchSize <= 0) {
    errors.push('Batch size must be greater than 0');
  }
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
  
  return true;
};

module.exports = config;
