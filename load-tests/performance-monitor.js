// Performance Monitoring for Load Tests
const os = require('os');
const axios = require('axios');
const config = require('./config');

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = [];
    this.startTime = null;
    this.monitoringInterval = null;
  }

  // Start monitoring
  start() {
    if (this.isMonitoring) {
      console.log('Performance monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    this.startTime = Date.now();
    this.metrics = [];

    console.log('🔍 Starting performance monitoring...');

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, config.monitoring.interval);
  }

  // Stop monitoring
  stop() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('🔍 Performance monitoring stopped');
    return this.getReport();
  }

  // Collect system metrics
  async collectMetrics() {
    try {
      const timestamp = Date.now();
      const metric = {
        timestamp,
        system: this.getSystemMetrics(),
        server: await this.getServerMetrics(),
        database: await this.getDatabaseMetrics()
      };

      this.metrics.push(metric);

      // Log real-time metrics if enabled
      if (config.reporting.realTimeUpdates) {
        this.logRealTimeMetrics(metric);
      }
    } catch (error) {
      console.error('Error collecting metrics:', error.message);
    }
  }

  // Get system metrics
  getSystemMetrics() {
    const cpus = os.cpus();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    return {
      cpu: {
        count: cpus.length,
        model: cpus[0]?.model || 'Unknown',
        loadAverage: os.loadavg()
      },
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        usagePercent: (usedMem / totalMem) * 100
      },
      uptime: os.uptime(),
      platform: os.platform(),
      arch: os.arch()
    };
  }

  // Get server metrics via API
  async getServerMetrics() {
    try {
      const startTime = Date.now();
      
      // Test server connectivity
      const pingResponse = await axios.get(config.getApiUrl('/ping'), {
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;

      // Get active users count
      let activeUsers = 0;
      try {
        const activeUsersResponse = await axios.get(config.getApiUrl('/debug/active-users'), {
          timeout: 5000
        });
        activeUsers = activeUsersResponse.data.count || 0;
      } catch (error) {
        // Ignore if endpoint doesn't exist
      }

      return {
        status: 'online',
        responseTime,
        activeUsers,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        status: 'offline',
        error: error.message,
        responseTime: null,
        activeUsers: 0,
        timestamp: Date.now()
      };
    }
  }

  // Get database metrics (if accessible)
  async getDatabaseMetrics() {
    try {
      // This would require a dedicated endpoint on the server
      // For now, we'll return basic connection test
      return {
        status: 'unknown',
        connections: null,
        operations: null,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  // Log real-time metrics
  logRealTimeMetrics(metric) {
    const { system, server } = metric;
    const memUsage = system.memory.usagePercent.toFixed(1);
    const cpuLoad = system.cpu.loadAverage[0].toFixed(2);
    const serverStatus = server.status;
    const responseTime = server.responseTime || 'N/A';
    const activeUsers = server.activeUsers;

    console.log(`📊 [${new Date().toISOString()}] CPU: ${cpuLoad} | MEM: ${memUsage}% | Server: ${serverStatus} (${responseTime}ms) | Users: ${activeUsers}`);
  }

  // Get monitoring report
  getReport() {
    if (this.metrics.length === 0) {
      return { error: 'No metrics collected' };
    }

    const duration = Date.now() - this.startTime;
    const systemMetrics = this.analyzeSystemMetrics();
    const serverMetrics = this.analyzeServerMetrics();

    return {
      duration,
      totalSamples: this.metrics.length,
      system: systemMetrics,
      server: serverMetrics,
      rawMetrics: this.metrics
    };
  }

  // Analyze system metrics
  analyzeSystemMetrics() {
    const memoryUsages = this.metrics.map(m => m.system.memory.usagePercent);
    const cpuLoads = this.metrics.map(m => m.system.cpu.loadAverage[0]);

    return {
      memory: {
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages),
        avg: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
        samples: memoryUsages.length
      },
      cpu: {
        min: Math.min(...cpuLoads),
        max: Math.max(...cpuLoads),
        avg: cpuLoads.reduce((a, b) => a + b, 0) / cpuLoads.length,
        samples: cpuLoads.length
      }
    };
  }

  // Analyze server metrics
  analyzeServerMetrics() {
    const responseTimes = this.metrics
      .map(m => m.server.responseTime)
      .filter(rt => rt !== null);
    
    const activeUserCounts = this.metrics.map(m => m.server.activeUsers);
    const onlineCount = this.metrics.filter(m => m.server.status === 'online').length;
    const offlineCount = this.metrics.filter(m => m.server.status === 'offline').length;

    return {
      availability: (onlineCount / this.metrics.length) * 100,
      responseTime: responseTimes.length > 0 ? {
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes),
        avg: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        samples: responseTimes.length
      } : null,
      activeUsers: {
        min: Math.min(...activeUserCounts),
        max: Math.max(...activeUserCounts),
        avg: activeUserCounts.reduce((a, b) => a + b, 0) / activeUserCounts.length,
        samples: activeUserCounts.length
      },
      uptime: {
        online: onlineCount,
        offline: offlineCount,
        total: this.metrics.length
      }
    };
  }

  // Get current system status
  getCurrentStatus() {
    if (this.metrics.length === 0) {
      return { error: 'No metrics available' };
    }

    const latest = this.metrics[this.metrics.length - 1];
    return {
      timestamp: latest.timestamp,
      system: {
        memoryUsage: latest.system.memory.usagePercent.toFixed(1) + '%',
        cpuLoad: latest.system.cpu.loadAverage[0].toFixed(2),
        freeMemory: this.formatBytes(latest.system.memory.free)
      },
      server: {
        status: latest.server.status,
        responseTime: latest.server.responseTime + 'ms',
        activeUsers: latest.server.activeUsers
      }
    };
  }

  // Format bytes helper
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = PerformanceMonitor;
