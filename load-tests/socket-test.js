// Socket.IO Connection Load Test
const io = require('socket.io-client');
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class SocketConnectionLoadTest {
  constructor() {
    this.results = [];
    this.activeConnections = 0;
    this.completedConnections = 0;
    this.sockets = [];
    this.monitor = new PerformanceMonitor();
    this.registeredUsers = [];
  }

  // Run socket connection load test
  async run(options = {}) {
    const {
      totalUsers = config.scenarios.socketConnection.concurrent,
      batchSize = config.test.batchSize,
      batchDelay = config.test.batchDelay,
      keepAliveTime = config.scenarios.socketConnection.keepAliveTime,
      useExistingUsers = false
    } = options;

    console.log(`🚀 Starting Socket.IO Connection Load Test`);
    console.log(`📊 Target: ${totalUsers} socket connections`);
    console.log(`📦 Batch size: ${batchSize}`);
    console.log(`⏱️  Batch delay: ${batchDelay}ms`);
    console.log(`🔗 Keep alive: ${utils.formatDuration(keepAliveTime)}`);
    console.log(`🎯 Server: ${config.getSocketUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Prepare users (register if needed)
      let users;
      if (useExistingUsers && this.registeredUsers.length >= totalUsers) {
        users = this.registeredUsers.slice(0, totalUsers);
        console.log(`👥 Using ${users.length} existing users`);
      } else {
        console.log('📝 Preparing users for socket connections...');
        users = await this.prepareUsers(totalUsers);
        console.log(`✅ ${users.length} users prepared`);
      }

      // Split into batches
      const batches = utils.batchArray(users, batchSize);
      console.log(`📦 Created ${batches.length} batches`);
      console.log('');

      // Process batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`📦 Processing batch ${i + 1}/${batches.length} (${batch.length} connections)`);
        
        // Process batch concurrently
        const batchPromises = batch.map(user => this.connectUser(user));
        await Promise.allSettled(batchPromises);
        
        // Progress update
        const progress = ((i + 1) / batches.length * 100).toFixed(1);
        console.log(`✅ Batch ${i + 1} completed. Progress: ${progress}%`);
        console.log(`🔗 Active connections: ${this.activeConnections}`);
        
        // Delay between batches (except for the last one)
        if (i < batches.length - 1) {
          await utils.sleep(batchDelay);
        }
      }

      console.log(`\n🔗 All connections established. Active: ${this.activeConnections}`);
      console.log(`⏱️  Keeping connections alive for ${utils.formatDuration(keepAliveTime)}...`);

      // Keep connections alive for specified time
      await utils.sleep(keepAliveTime);

      console.log('\n🔌 Closing all connections...');
      await this.closeAllConnections();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      console.log('');
      console.log('📊 Socket Connection Load Test Results:');
      console.log(`⏱️  Duration: ${utils.formatDuration(duration)}`);
      console.log(`✅ Successful Connections: ${report.summary.successes}`);
      console.log(`❌ Failed Connections: ${report.summary.errors}`);
      console.log(`📈 Success Rate: ${(100 - report.summary.errorRate).toFixed(2)}%`);
      console.log(`🔗 Peak Concurrent: ${report.peakConcurrent}`);
      console.log(`📊 Avg Connection Time: ${report.summary.responseTime.avg.toFixed(2)}ms`);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('socket-connection-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Socket connection load test failed:', error);
      await this.closeAllConnections();
      this.monitor.stop();
      throw error;
    }
  }

  // Prepare users by registering and logging them in
  async prepareUsers(count) {
    const users = [];
    const batchSize = 50;
    
    for (let i = 0; i < count; i++) {
      users.push(utils.generateUser(i + 1));
    }

    // Register and login users in batches
    const batches = utils.batchArray(users, batchSize);
    const preparedUsers = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      // Register users
      const registrationPromises = batch.map(user => this.registerUser(user));
      const registrationResults = await Promise.allSettled(registrationPromises);
      
      // Login successful registrations
      const loginPromises = [];
      registrationResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          loginPromises.push(this.loginUser(batch[index]));
        }
      });
      
      const loginResults = await Promise.allSettled(loginPromises);
      
      loginResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          const user = batch[index];
          user.userId = result.value.userId;
          preparedUsers.push(user);
        }
      });

      // Small delay between preparation batches
      if (i < batches.length - 1) {
        await utils.sleep(500);
      }
    }

    this.registeredUsers = preparedUsers;
    return preparedUsers;
  }

  // Register a user
  async registerUser(user) {
    try {
      await axios.post(config.getApiUrl('/register'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Login user
  async loginUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/login'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return {
        success: true,
        userId: response.data.userId
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Connect a user via Socket.IO
  async connectUser(user) {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      try {
        const socket = io(config.getSocketUrl(), {
          timeout: 10000,
          forceNew: true
        });

        let connected = false;
        let registered = false;

        // Connection timeout
        const timeout = setTimeout(() => {
          if (!connected) {
            socket.disconnect();
            this.recordResult(user, startTime, false, 'CONNECTION_TIMEOUT');
            resolve();
          }
        }, 15000);

        socket.on('connect', () => {
          connected = true;
          this.activeConnections++;
          
          // Register user with server
          socket.emit('register', {
            userId: user.userId,
            username: user.username
          });
        });

        socket.on('disconnect', () => {
          if (this.activeConnections > 0) {
            this.activeConnections--;
          }
        });

        socket.on('error', (error) => {
          clearTimeout(timeout);
          socket.disconnect();
          this.recordResult(user, startTime, false, 'SOCKET_ERROR', error.message);
          resolve();
        });

        // Listen for successful registration (if server sends confirmation)
        socket.on('registered', () => {
          registered = true;
          clearTimeout(timeout);
          this.recordResult(user, startTime, true);
          this.sockets.push(socket);
          resolve();
        });

        // Fallback: assume registration successful after connection
        setTimeout(() => {
          if (connected && !registered) {
            registered = true;
            clearTimeout(timeout);
            this.recordResult(user, startTime, true);
            this.sockets.push(socket);
            resolve();
          }
        }, 2000);

      } catch (error) {
        this.recordResult(user, startTime, false, 'CONNECTION_ERROR', error.message);
        resolve();
      }
    });
  }

  // Record connection result
  recordResult(user, startTime, success, errorType = null, errorMessage = null) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    const result = {
      userId: user.id,
      username: user.username,
      actualUserId: user.userId,
      responseTime,
      timestamp: endTime,
      success,
      errorType,
      error: errorMessage
    };

    this.results.push(result);
    this.completedConnections++;
  }

  // Close all socket connections
  async closeAllConnections() {
    console.log(`🔌 Closing ${this.sockets.length} socket connections...`);
    
    const closePromises = this.sockets.map(socket => {
      return new Promise((resolve) => {
        socket.on('disconnect', resolve);
        socket.disconnect();
        setTimeout(resolve, 1000); // Fallback timeout
      });
    });

    await Promise.allSettled(closePromises);
    this.sockets = [];
    this.activeConnections = 0;
    
    console.log('✅ All connections closed');
  }

  // Generate test report
  generateReport(duration, monitoringReport) {
    const summary = utils.calculateSummary(this.results);
    
    // Error analysis
    const errorTypes = {};
    this.results.filter(r => !r.success).forEach(r => {
      errorTypes[r.errorType] = (errorTypes[r.errorType] || 0) + 1;
    });

    // Connection time distribution
    const connectionTimes = this.results.filter(r => r.success).map(r => r.responseTime);
    const timeDistribution = {
      '0-100ms': connectionTimes.filter(t => t <= 100).length,
      '100ms-500ms': connectionTimes.filter(t => t > 100 && t <= 500).length,
      '500ms-1s': connectionTimes.filter(t => t > 500 && t <= 1000).length,
      '1s-3s': connectionTimes.filter(t => t > 1000 && t <= 3000).length,
      '3s+': connectionTimes.filter(t => t > 3000).length
    };

    // Calculate peak concurrent connections
    const peakConcurrent = Math.max(...this.results.map((_, index) => {
      const timestamp = this.results[index].timestamp;
      return this.results.filter(r => 
        r.success && 
        r.timestamp <= timestamp && 
        r.timestamp >= timestamp - config.scenarios.socketConnection.keepAliveTime
      ).length;
    }));

    return {
      testType: 'socket-connection',
      duration,
      summary,
      errorTypes,
      timeDistribution,
      peakConcurrent,
      monitoring: monitoringReport,
      thresholds: {
        responseTime: {
          p95: summary.responseTime.p95 <= config.thresholds.responseTime.p95,
          p99: summary.responseTime.p99 <= config.thresholds.responseTime.p99
        },
        errorRate: summary.errorRate <= config.thresholds.errorRate * 100,
        throughput: summary.throughput >= config.thresholds.throughput.socketConnection
      },
      rawResults: this.results
    };
  }

  // Get current progress
  getProgress() {
    return {
      activeConnections: this.activeConnections,
      completedConnections: this.completedConnections,
      totalResults: this.results.length,
      successRate: this.results.length > 0 ? 
        (this.results.filter(r => r.success).length / this.results.length * 100).toFixed(2) : 0
    };
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new SocketConnectionLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--keep-alive=')) {
      options.keepAliveTime = parseInt(arg.split('=')[1]);
    } else if (arg === '--use-existing') {
      options.useExistingUsers = true;
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Socket connection load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Socket connection load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = SocketConnectionLoadTest;
