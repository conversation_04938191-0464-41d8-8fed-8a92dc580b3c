// User Registration Load Test
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class RegistrationLoadTest {
  constructor() {
    this.results = [];
    this.activeRequests = 0;
    this.completedRequests = 0;
    this.monitor = new PerformanceMonitor();
  }

  // Run registration load test
  async run(options = {}) {
    const {
      totalUsers = config.scenarios.registration.concurrent,
      batchSize = config.test.batchSize,
      batchDelay = config.test.batchDelay
    } = options;

    console.log(`🚀 Starting Registration Load Test`);
    console.log(`📊 Target: ${totalUsers} registrations`);
    console.log(`📦 Batch size: ${batchSize}`);
    console.log(`⏱️  Batch delay: ${batchDelay}ms`);
    console.log(`🎯 Server: ${config.getServerUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Generate user data
      console.log('📝 Generating user data...');
      const users = [];
      for (let i = 0; i < totalUsers; i++) {
        users.push(utils.generateUser(i + 1));
      }

      // Split into batches
      const batches = utils.batchArray(users, batchSize);
      console.log(`📦 Created ${batches.length} batches`);
      console.log('');

      // Process batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`📦 Processing batch ${i + 1}/${batches.length} (${batch.length} users)`);
        
        // Process batch concurrently
        const batchPromises = batch.map(user => this.registerUser(user));
        await Promise.allSettled(batchPromises);
        
        // Progress update
        const progress = ((i + 1) / batches.length * 100).toFixed(1);
        console.log(`✅ Batch ${i + 1} completed. Progress: ${progress}%`);
        
        // Delay between batches (except for the last one)
        if (i < batches.length - 1) {
          await utils.sleep(batchDelay);
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      console.log('');
      console.log('📊 Registration Load Test Results:');
      console.log(`⏱️  Duration: ${utils.formatDuration(duration)}`);
      console.log(`✅ Successful: ${report.summary.successes}`);
      console.log(`❌ Failed: ${report.summary.errors}`);
      console.log(`📈 Success Rate: ${(100 - report.summary.errorRate).toFixed(2)}%`);
      console.log(`⚡ Throughput: ${report.summary.throughput.toFixed(2)} req/s`);
      console.log(`📊 Avg Response Time: ${report.summary.responseTime.avg.toFixed(2)}ms`);
      console.log(`📊 95th Percentile: ${report.summary.responseTime.p95.toFixed(2)}ms`);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('registration-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Registration load test failed:', error);
      this.monitor.stop();
      throw error;
    }
  }

  // Register a single user
  async registerUser(user) {
    const startTime = Date.now();
    this.activeRequests++;

    try {
      const response = await axios.post(config.getApiUrl('/register'), {
        username: user.username,
        password: user.password
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const result = {
        userId: user.id,
        username: user.username,
        status: response.status,
        responseTime,
        timestamp: endTime,
        success: true
      };

      this.results.push(result);
      this.completedRequests++;
      
      return result;

    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const result = {
        userId: user.id,
        username: user.username,
        status: error.response?.status || 0,
        responseTime,
        timestamp: endTime,
        success: false,
        error: error.message,
        errorType: this.categorizeError(error)
      };

      this.results.push(result);
      this.completedRequests++;
      
      return result;

    } finally {
      this.activeRequests--;
    }
  }

  // Categorize error types
  categorizeError(error) {
    if (error.code === 'ECONNREFUSED') {
      return 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      return 'TIMEOUT';
    } else if (error.response?.status === 400) {
      return 'BAD_REQUEST';
    } else if (error.response?.status === 500) {
      return 'SERVER_ERROR';
    } else if (error.response?.status === 409) {
      return 'CONFLICT'; // Username already exists
    } else {
      return 'UNKNOWN';
    }
  }

  // Generate test report
  generateReport(duration, monitoringReport) {
    const summary = utils.calculateSummary(this.results);
    
    // Error analysis
    const errorTypes = {};
    this.results.filter(r => !r.success).forEach(r => {
      errorTypes[r.errorType] = (errorTypes[r.errorType] || 0) + 1;
    });

    // Response time distribution
    const responseTimes = this.results.map(r => r.responseTime);
    const timeDistribution = {
      '0-500ms': responseTimes.filter(t => t <= 500).length,
      '500ms-1s': responseTimes.filter(t => t > 500 && t <= 1000).length,
      '1s-2s': responseTimes.filter(t => t > 1000 && t <= 2000).length,
      '2s-5s': responseTimes.filter(t => t > 2000 && t <= 5000).length,
      '5s+': responseTimes.filter(t => t > 5000).length
    };

    return {
      testType: 'registration',
      duration,
      summary,
      errorTypes,
      timeDistribution,
      monitoring: monitoringReport,
      thresholds: {
        responseTime: {
          p95: summary.responseTime.p95 <= config.thresholds.responseTime.p95,
          p99: summary.responseTime.p99 <= config.thresholds.responseTime.p99
        },
        errorRate: summary.errorRate <= config.thresholds.errorRate * 100,
        throughput: summary.throughput >= config.thresholds.throughput.registration
      },
      rawResults: this.results
    };
  }

  // Get current progress
  getProgress() {
    return {
      activeRequests: this.activeRequests,
      completedRequests: this.completedRequests,
      totalResults: this.results.length,
      successRate: this.results.length > 0 ? 
        (this.results.filter(r => r.success).length / this.results.length * 100).toFixed(2) : 0
    };
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new RegistrationLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--batch-delay=')) {
      options.batchDelay = parseInt(arg.split('=')[1]);
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Registration load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Registration load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = RegistrationLoadTest;
