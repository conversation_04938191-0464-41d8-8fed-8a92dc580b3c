// Profile Update Load Test
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class ProfileUpdateLoadTest {
  constructor() {
    this.results = [];
    this.activeRequests = 0;
    this.completedRequests = 0;
    this.monitor = new PerformanceMonitor();
    this.registeredUsers = [];
  }

  // Run profile update load test
  async run(options = {}) {
    const {
      totalUsers = config.scenarios.profileUpdate.concurrent,
      batchSize = config.test.batchSize,
      batchDelay = config.test.batchDelay,
      useExistingUsers = false
    } = options;

    console.log(`🚀 Starting Profile Update Load Test`);
    console.log(`📊 Target: ${totalUsers} profile updates`);
    console.log(`📦 Batch size: ${batchSize}`);
    console.log(`⏱️  Batch delay: ${batchDelay}ms`);
    console.log(`🎯 Server: ${config.getServerUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Prepare users (register if needed)
      let users;
      if (useExistingUsers && this.registeredUsers.length >= totalUsers) {
        users = this.registeredUsers.slice(0, totalUsers);
        console.log(`👥 Using ${users.length} existing users`);
      } else {
        console.log('📝 Registering users for profile updates...');
        users = await this.prepareUsers(totalUsers);
        console.log(`✅ ${users.length} users prepared`);
      }

      // Split into batches
      const batches = utils.batchArray(users, batchSize);
      console.log(`📦 Created ${batches.length} batches`);
      console.log('');

      // Process batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`📦 Processing batch ${i + 1}/${batches.length} (${batch.length} users)`);
        
        // Process batch concurrently
        const batchPromises = batch.map(user => this.updateUserProfile(user));
        await Promise.allSettled(batchPromises);
        
        // Progress update
        const progress = ((i + 1) / batches.length * 100).toFixed(1);
        console.log(`✅ Batch ${i + 1} completed. Progress: ${progress}%`);
        
        // Delay between batches (except for the last one)
        if (i < batches.length - 1) {
          await utils.sleep(batchDelay);
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      console.log('');
      console.log('📊 Profile Update Load Test Results:');
      console.log(`⏱️  Duration: ${utils.formatDuration(duration)}`);
      console.log(`✅ Successful: ${report.summary.successes}`);
      console.log(`❌ Failed: ${report.summary.errors}`);
      console.log(`📈 Success Rate: ${(100 - report.summary.errorRate).toFixed(2)}%`);
      console.log(`⚡ Throughput: ${report.summary.throughput.toFixed(2)} req/s`);
      console.log(`📊 Avg Response Time: ${report.summary.responseTime.avg.toFixed(2)}ms`);
      console.log(`📊 95th Percentile: ${report.summary.responseTime.p95.toFixed(2)}ms`);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('profile-update-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Profile update load test failed:', error);
      this.monitor.stop();
      throw error;
    }
  }

  // Prepare users by registering them first
  async prepareUsers(count) {
    const users = [];
    const batchSize = 50; // Smaller batches for registration
    
    for (let i = 0; i < count; i++) {
      users.push(utils.generateUser(i + 1));
    }

    // Register users in batches
    const batches = utils.batchArray(users, batchSize);
    const registeredUsers = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const registrationPromises = batch.map(user => this.registerUser(user));
      const results = await Promise.allSettled(registrationPromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          registeredUsers.push(batch[index]);
        }
      });

      // Small delay between registration batches
      if (i < batches.length - 1) {
        await utils.sleep(500);
      }
    }

    this.registeredUsers = registeredUsers;
    return registeredUsers;
  }

  // Register a user (helper for preparation)
  async registerUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/register'), {
        username: user.username,
        password: user.password
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        userId: response.data.userId || user.id,
        username: user.username
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        username: user.username
      };
    }
  }

  // Login user to get userId
  async loginUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/login'), {
        username: user.username,
        password: user.password
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        userId: response.data.userId,
        user: response.data.user
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Update user profile
  async updateUserProfile(user) {
    const startTime = Date.now();
    this.activeRequests++;

    try {
      // First login to get userId
      const loginResult = await this.loginUser(user);
      if (!loginResult.success) {
        throw new Error(`Login failed: ${loginResult.error}`);
      }

      const userId = loginResult.userId;

      // Prepare profile update data
      const profileData = {
        age: user.age,
        description: user.description,
        passions: user.passions,
        images: config.scenarios.profileUpdate.imageUploadChance > Math.random() ? 
          user.images : []
      };

      // Update profile
      const response = await axios.put(config.getApiUrl(`/profile/${userId}`), profileData, {
        timeout: 15000, // Longer timeout for image uploads
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const result = {
        userId: user.id,
        username: user.username,
        actualUserId: userId,
        status: response.status,
        responseTime,
        timestamp: endTime,
        success: true,
        profileData: {
          age: profileData.age,
          passionsCount: profileData.passions.length,
          imagesCount: profileData.images.length,
          descriptionLength: profileData.description.length
        }
      };

      this.results.push(result);
      this.completedRequests++;
      
      return result;

    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const result = {
        userId: user.id,
        username: user.username,
        status: error.response?.status || 0,
        responseTime,
        timestamp: endTime,
        success: false,
        error: error.message,
        errorType: this.categorizeError(error)
      };

      this.results.push(result);
      this.completedRequests++;
      
      return result;

    } finally {
      this.activeRequests--;
    }
  }

  // Categorize error types
  categorizeError(error) {
    if (error.code === 'ECONNREFUSED') {
      return 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      return 'TIMEOUT';
    } else if (error.response?.status === 400) {
      return 'BAD_REQUEST';
    } else if (error.response?.status === 401) {
      return 'UNAUTHORIZED';
    } else if (error.response?.status === 404) {
      return 'NOT_FOUND';
    } else if (error.response?.status === 500) {
      return 'SERVER_ERROR';
    } else if (error.message.includes('Login failed')) {
      return 'LOGIN_FAILED';
    } else {
      return 'UNKNOWN';
    }
  }

  // Generate test report
  generateReport(duration, monitoringReport) {
    const summary = utils.calculateSummary(this.results);
    
    // Error analysis
    const errorTypes = {};
    this.results.filter(r => !r.success).forEach(r => {
      errorTypes[r.errorType] = (errorTypes[r.errorType] || 0) + 1;
    });

    // Profile data analysis
    const successfulUpdates = this.results.filter(r => r.success && r.profileData);
    const profileStats = {
      avgAge: successfulUpdates.length > 0 ? 
        successfulUpdates.reduce((sum, r) => sum + r.profileData.age, 0) / successfulUpdates.length : 0,
      avgPassions: successfulUpdates.length > 0 ? 
        successfulUpdates.reduce((sum, r) => sum + r.profileData.passionsCount, 0) / successfulUpdates.length : 0,
      avgImages: successfulUpdates.length > 0 ? 
        successfulUpdates.reduce((sum, r) => sum + r.profileData.imagesCount, 0) / successfulUpdates.length : 0,
      avgDescriptionLength: successfulUpdates.length > 0 ? 
        successfulUpdates.reduce((sum, r) => sum + r.profileData.descriptionLength, 0) / successfulUpdates.length : 0
    };

    // Response time distribution
    const responseTimes = this.results.map(r => r.responseTime);
    const timeDistribution = {
      '0-1s': responseTimes.filter(t => t <= 1000).length,
      '1s-3s': responseTimes.filter(t => t > 1000 && t <= 3000).length,
      '3s-5s': responseTimes.filter(t => t > 3000 && t <= 5000).length,
      '5s-10s': responseTimes.filter(t => t > 5000 && t <= 10000).length,
      '10s+': responseTimes.filter(t => t > 10000).length
    };

    return {
      testType: 'profile-update',
      duration,
      summary,
      errorTypes,
      profileStats,
      timeDistribution,
      monitoring: monitoringReport,
      thresholds: {
        responseTime: {
          p95: summary.responseTime.p95 <= config.thresholds.responseTime.p95,
          p99: summary.responseTime.p99 <= config.thresholds.responseTime.p99
        },
        errorRate: summary.errorRate <= config.thresholds.errorRate * 100,
        throughput: summary.throughput >= config.thresholds.throughput.profileUpdate
      },
      rawResults: this.results
    };
  }

  // Get current progress
  getProgress() {
    return {
      activeRequests: this.activeRequests,
      completedRequests: this.completedRequests,
      totalResults: this.results.length,
      successRate: this.results.length > 0 ? 
        (this.results.filter(r => r.success).length / this.results.length * 100).toFixed(2) : 0
    };
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new ProfileUpdateLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--batch-delay=')) {
      options.batchDelay = parseInt(arg.split('=')[1]);
    } else if (arg === '--use-existing') {
      options.useExistingUsers = true;
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Profile update load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Profile update load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = ProfileUpdateLoadTest;
