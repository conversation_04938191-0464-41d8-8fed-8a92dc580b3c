// Messaging Load Test
const io = require('socket.io-client');
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class MessagingLoadTest {
  constructor() {
    this.results = [];
    this.messages = [];
    this.activeConnections = 0;
    this.completedMessages = 0;
    this.sockets = [];
    this.monitor = new PerformanceMonitor();
    this.registeredUsers = [];
    this.userPairs = [];
  }

  // Run messaging load test
  async run(options = {}) {
    const {
      totalUsers = config.scenarios.messaging.concurrent,
      batchSize = config.test.batchSize,
      batchDelay = config.test.batchDelay,
      messagesPerUser = config.scenarios.messaging.messagesPerUser,
      messageInterval = config.scenarios.messaging.messageInterval,
      testDuration = config.test.testDuration,
      useExistingUsers = false
    } = options;

    console.log(`🚀 Starting Messaging Load Test`);
    console.log(`📊 Target: ${totalUsers} users sending messages`);
    console.log(`📦 Batch size: ${batchSize}`);
    console.log(`💬 Messages per user: ${messagesPerUser}`);
    console.log(`⏱️  Message interval: ${utils.formatDuration(messageInterval)}`);
    console.log(`🎯 Server: ${config.getSocketUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Prepare users
      let users;
      if (useExistingUsers && this.registeredUsers.length >= totalUsers) {
        users = this.registeredUsers.slice(0, totalUsers);
        console.log(`👥 Using ${users.length} existing users`);
      } else {
        console.log('📝 Preparing users for messaging test...');
        users = await this.prepareUsers(totalUsers);
        console.log(`✅ ${users.length} users prepared`);
      }

      // Connect all users
      console.log('🔗 Connecting users...');
      await this.connectAllUsers(users, batchSize, batchDelay);
      console.log(`✅ ${this.activeConnections} users connected`);

      // Create user pairs for messaging
      console.log('👥 Creating user pairs...');
      this.createUserPairs();
      console.log(`✅ ${this.userPairs.length} user pairs created`);

      // Start messaging simulation
      console.log('\n💬 Starting message simulation...');
      await this.simulateMessaging(messagesPerUser, messageInterval);

      console.log('\n🔌 Closing all connections...');
      await this.closeAllConnections();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      console.log('');
      console.log('📊 Messaging Load Test Results:');
      console.log(`⏱️  Duration: ${utils.formatDuration(duration)}`);
      console.log(`💬 Total Messages: ${this.messages.length}`);
      console.log(`✅ Successful: ${report.summary.successes}`);
      console.log(`❌ Failed: ${report.summary.errors}`);
      console.log(`📈 Success Rate: ${(100 - report.summary.errorRate).toFixed(2)}%`);
      console.log(`⚡ Messages/sec: ${(this.messages.length / (duration / 1000)).toFixed(2)}`);
      console.log(`📊 Avg Response Time: ${report.summary.responseTime.avg.toFixed(2)}ms`);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('messaging-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Messaging load test failed:', error);
      await this.closeAllConnections();
      this.monitor.stop();
      throw error;
    }
  }

  // Prepare users by registering and logging them in
  async prepareUsers(count) {
    const users = [];
    const batchSize = 50;
    
    for (let i = 0; i < count; i++) {
      users.push(utils.generateUser(i + 1));
    }

    // Register and login users in batches
    const batches = utils.batchArray(users, batchSize);
    const preparedUsers = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      // Register users
      const registrationPromises = batch.map(user => this.registerUser(user));
      const registrationResults = await Promise.allSettled(registrationPromises);
      
      // Login successful registrations
      const loginPromises = [];
      registrationResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          loginPromises.push(this.loginUser(batch[index]));
        }
      });
      
      const loginResults = await Promise.allSettled(loginPromises);
      
      loginResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          const user = batch[index];
          user.userId = result.value.userId;
          preparedUsers.push(user);
        }
      });

      if (i < batches.length - 1) {
        await utils.sleep(500);
      }
    }

    this.registeredUsers = preparedUsers;
    return preparedUsers;
  }

  // Register a user
  async registerUser(user) {
    try {
      await axios.post(config.getApiUrl('/register'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Login user
  async loginUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/login'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return {
        success: true,
        userId: response.data.userId
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Connect all users
  async connectAllUsers(users, batchSize, batchDelay) {
    const batches = utils.batchArray(users, batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const connectionPromises = batch.map(user => this.connectUser(user));
      await Promise.allSettled(connectionPromises);
      
      if (i < batches.length - 1) {
        await utils.sleep(batchDelay);
      }
    }
  }

  // Connect a user via Socket.IO
  async connectUser(user) {
    return new Promise((resolve) => {
      try {
        const socket = io(config.getSocketUrl(), {
          timeout: 10000,
          forceNew: true
        });

        let connected = false;

        const timeout = setTimeout(() => {
          if (!connected) {
            socket.disconnect();
            resolve();
          }
        }, 15000);

        socket.on('connect', () => {
          connected = true;
          this.activeConnections++;
          
          // Register user with server
          socket.emit('register', {
            userId: user.userId,
            username: user.username
          });

          // Listen for incoming messages
          socket.on('message', (messageData) => {
            // Record received message
            this.recordMessageReceived(messageData);
          });

          clearTimeout(timeout);
          this.sockets.push({ socket, user });
          resolve();
        });

        socket.on('error', () => {
          clearTimeout(timeout);
          socket.disconnect();
          resolve();
        });

      } catch (error) {
        resolve();
      }
    });
  }

  // Create user pairs for messaging
  createUserPairs() {
    const users = this.sockets.map(s => s.user);
    
    // Create pairs randomly
    for (let i = 0; i < users.length - 1; i += 2) {
      if (i + 1 < users.length) {
        this.userPairs.push({
          user1: users[i],
          user2: users[i + 1],
          socket1: this.sockets.find(s => s.user.id === users[i].id).socket,
          socket2: this.sockets.find(s => s.user.id === users[i + 1].id).socket
        });
      }
    }
  }

  // Simulate messaging between user pairs
  async simulateMessaging(messagesPerUser, messageInterval) {
    const totalMessages = this.userPairs.length * messagesPerUser * 2; // Each user sends messages
    console.log(`💬 Sending ${totalMessages} messages...`);

    const messagePromises = [];

    for (const pair of this.userPairs) {
      // User 1 sends messages to User 2
      for (let i = 0; i < messagesPerUser; i++) {
        messagePromises.push(
          this.scheduleMessage(pair.socket1, pair.user1, pair.user2, i * messageInterval)
        );
      }

      // User 2 sends messages to User 1
      for (let i = 0; i < messagesPerUser; i++) {
        messagePromises.push(
          this.scheduleMessage(pair.socket2, pair.user2, pair.user1, i * messageInterval + messageInterval / 2)
        );
      }
    }

    // Wait for all messages to be sent
    await Promise.allSettled(messagePromises);
    
    // Wait a bit more for any pending message deliveries
    await utils.sleep(5000);
  }

  // Schedule a message to be sent after a delay
  async scheduleMessage(socket, sender, receiver, delay) {
    await utils.sleep(delay);
    return this.sendMessage(socket, sender, receiver);
  }

  // Send a message
  async sendMessage(socket, sender, receiver) {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      try {
        const messageData = {
          id: utils.generateMessageId(),
          senderId: sender.userId,
          senderUsername: sender.username,
          receiverId: receiver.userId,
          receiverUsername: receiver.username,
          text: utils.generateMessage(),
          timestamp: Date.now()
        };

        socket.emit('sendMessage', messageData);
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        const result = {
          messageId: messageData.id,
          senderId: sender.userId,
          receiverId: receiver.userId,
          responseTime,
          timestamp: endTime,
          success: true,
          messageLength: messageData.text.length
        };

        this.results.push(result);
        this.messages.push(messageData);
        this.completedMessages++;
        
        resolve(result);

      } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        const result = {
          senderId: sender.userId,
          receiverId: receiver.userId,
          responseTime,
          timestamp: endTime,
          success: false,
          error: error.message,
          errorType: 'SEND_ERROR'
        };

        this.results.push(result);
        this.completedMessages++;
        
        resolve(result);
      }
    });
  }

  // Record received message
  recordMessageReceived(messageData) {
    // This could be used to track message delivery success
    // For now, we'll just log it
  }

  // Close all socket connections
  async closeAllConnections() {
    const closePromises = this.sockets.map(({ socket }) => {
      return new Promise((resolve) => {
        socket.on('disconnect', resolve);
        socket.disconnect();
        setTimeout(resolve, 1000);
      });
    });

    await Promise.allSettled(closePromises);
    this.sockets = [];
    this.activeConnections = 0;
  }

  // Generate test report
  generateReport(duration, monitoringReport) {
    const summary = utils.calculateSummary(this.results);
    
    // Message statistics
    const messageStats = {
      totalMessages: this.messages.length,
      completedMessages: this.completedMessages,
      messagesPerSecond: this.messages.length / (duration / 1000),
      userPairs: this.userPairs.length,
      avgMessageLength: this.messages.length > 0 ? 
        this.messages.reduce((sum, m) => sum + m.text.length, 0) / this.messages.length : 0
    };

    // Error analysis
    const errorTypes = {};
    this.results.filter(r => !r.success).forEach(r => {
      errorTypes[r.errorType] = (errorTypes[r.errorType] || 0) + 1;
    });

    // Response time distribution
    const responseTimes = this.results.filter(r => r.success).map(r => r.responseTime);
    const timeDistribution = {
      '0-10ms': responseTimes.filter(t => t <= 10).length,
      '10ms-50ms': responseTimes.filter(t => t > 10 && t <= 50).length,
      '50ms-100ms': responseTimes.filter(t => t > 50 && t <= 100).length,
      '100ms-500ms': responseTimes.filter(t => t > 100 && t <= 500).length,
      '500ms+': responseTimes.filter(t => t > 500).length
    };

    // Message timeline
    const messageTimeline = {};
    if (this.messages.length > 0) {
      const startTime = Math.min(...this.messages.map(m => m.timestamp));
      const intervals = Math.ceil(duration / 10000); // 10-second intervals
      
      for (let i = 0; i < intervals; i++) {
        const intervalStart = startTime + (i * 10000);
        const intervalEnd = intervalStart + 10000;
        const count = this.messages.filter(m => m.timestamp >= intervalStart && m.timestamp < intervalEnd).length;
        messageTimeline[`${(i * 10)}s`] = count;
      }
    }

    return {
      testType: 'messaging',
      duration,
      summary,
      messageStats,
      errorTypes,
      timeDistribution,
      messageTimeline,
      monitoring: monitoringReport,
      thresholds: {
        responseTime: {
          p95: summary.responseTime.p95 <= config.thresholds.responseTime.p95,
          p99: summary.responseTime.p99 <= config.thresholds.responseTime.p99
        },
        errorRate: summary.errorRate <= config.thresholds.errorRate * 100,
        throughput: messageStats.messagesPerSecond >= config.thresholds.throughput.messaging
      },
      rawMessages: this.messages,
      rawResults: this.results
    };
  }

  // Get current progress
  getProgress() {
    return {
      activeConnections: this.activeConnections,
      completedMessages: this.completedMessages,
      totalMessages: this.messages.length,
      userPairs: this.userPairs.length,
      successRate: this.results.length > 0 ? 
        (this.results.filter(r => r.success).length / this.results.length * 100).toFixed(2) : 0
    };
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new MessagingLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--messages=')) {
      options.messagesPerUser = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--interval=')) {
      options.messageInterval = parseInt(arg.split('=')[1]);
    } else if (arg === '--use-existing') {
      options.useExistingUsers = true;
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Messaging load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Messaging load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = MessagingLoadTest;
