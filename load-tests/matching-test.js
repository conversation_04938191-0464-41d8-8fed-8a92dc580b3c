// Matching System Load Test
const io = require('socket.io-client');
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class MatchingLoadTest {
  constructor() {
    this.results = [];
    this.matches = [];
    this.activeConnections = 0;
    this.completedShakes = 0;
    this.sockets = [];
    this.monitor = new PerformanceMonitor();
    this.registeredUsers = [];
  }

  // Run matching load test
  async run(options = {}) {
    const {
      totalUsers = config.scenarios.matching.concurrent,
      batchSize = config.test.batchSize,
      batchDelay = config.test.batchDelay,
      shakeInterval = config.scenarios.matching.shakeInterval,
      testDuration = config.test.testDuration,
      useExistingUsers = false
    } = options;

    console.log(`🚀 Starting Matching System Load Test`);
    console.log(`📊 Target: ${totalUsers} users shaking for matches`);
    console.log(`📦 Batch size: ${batchSize}`);
    console.log(`⏱️  Test duration: ${utils.formatDuration(testDuration)}`);
    console.log(`🤝 Shake interval: ${utils.formatDuration(shakeInterval)}`);
    console.log(`🎯 Server: ${config.getSocketUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Prepare users
      let users;
      if (useExistingUsers && this.registeredUsers.length >= totalUsers) {
        users = this.registeredUsers.slice(0, totalUsers);
        console.log(`👥 Using ${users.length} existing users`);
      } else {
        console.log('📝 Preparing users for matching test...');
        users = await this.prepareUsers(totalUsers);
        console.log(`✅ ${users.length} users prepared`);
      }

      // Connect all users
      console.log('🔗 Connecting users...');
      await this.connectAllUsers(users, batchSize, batchDelay);
      console.log(`✅ ${this.activeConnections} users connected`);

      // Start shaking simulation
      console.log('\n🤝 Starting shake simulation...');
      const shakePromise = this.simulateShaking(shakeInterval, testDuration);
      
      // Wait for test duration
      await shakePromise;

      console.log('\n🔌 Closing all connections...');
      await this.closeAllConnections();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      console.log('');
      console.log('📊 Matching System Load Test Results:');
      console.log(`⏱️  Duration: ${utils.formatDuration(duration)}`);
      console.log(`🤝 Total Shakes: ${this.completedShakes}`);
      console.log(`💕 Total Matches: ${this.matches.length}`);
      console.log(`📈 Match Rate: ${((this.matches.length / this.completedShakes) * 100).toFixed(2)}%`);
      console.log(`⚡ Shakes/sec: ${(this.completedShakes / (duration / 1000)).toFixed(2)}`);
      console.log(`💕 Matches/sec: ${(this.matches.length / (duration / 1000)).toFixed(2)}`);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('matching-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Matching load test failed:', error);
      await this.closeAllConnections();
      this.monitor.stop();
      throw error;
    }
  }

  // Prepare users by registering and logging them in
  async prepareUsers(count) {
    const users = [];
    const batchSize = 50;
    
    // Generate users with varied locations for better matching
    for (let i = 0; i < count; i++) {
      const user = utils.generateUser(i + 1);
      // Cluster users in different areas for realistic matching
      const clusterIndex = i % 5;
      const clusterCenters = [
        { lat: 40.7128, lng: -74.0060 }, // NYC
        { lat: 34.0522, lng: -118.2437 }, // LA
        { lat: 41.8781, lng: -87.6298 }, // Chicago
        { lat: 29.7604, lng: -95.3698 }, // Houston
        { lat: 33.4484, lng: -112.0740 }  // Phoenix
      ];
      
      user.location = utils.generateLocation(
        clusterCenters[clusterIndex].lat,
        clusterCenters[clusterIndex].lng,
        config.scenarios.matching.locationRadius
      );
      
      users.push(user);
    }

    // Register and login users in batches
    const batches = utils.batchArray(users, batchSize);
    const preparedUsers = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      // Register users
      const registrationPromises = batch.map(user => this.registerUser(user));
      const registrationResults = await Promise.allSettled(registrationPromises);
      
      // Login successful registrations
      const loginPromises = [];
      registrationResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          loginPromises.push(this.loginUser(batch[index]));
        }
      });
      
      const loginResults = await Promise.allSettled(loginPromises);
      
      loginResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          const user = batch[index];
          user.userId = result.value.userId;
          preparedUsers.push(user);
        }
      });

      if (i < batches.length - 1) {
        await utils.sleep(500);
      }
    }

    this.registeredUsers = preparedUsers;
    return preparedUsers;
  }

  // Register a user
  async registerUser(user) {
    try {
      await axios.post(config.getApiUrl('/register'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Login user
  async loginUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/login'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });

      return {
        success: true,
        userId: response.data.userId
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Connect all users
  async connectAllUsers(users, batchSize, batchDelay) {
    const batches = utils.batchArray(users, batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const connectionPromises = batch.map(user => this.connectUser(user));
      await Promise.allSettled(connectionPromises);
      
      if (i < batches.length - 1) {
        await utils.sleep(batchDelay);
      }
    }
  }

  // Connect a user via Socket.IO
  async connectUser(user) {
    return new Promise((resolve) => {
      try {
        const socket = io(config.getSocketUrl(), {
          timeout: 10000,
          forceNew: true
        });

        let connected = false;

        const timeout = setTimeout(() => {
          if (!connected) {
            socket.disconnect();
            resolve();
          }
        }, 15000);

        socket.on('connect', () => {
          connected = true;
          this.activeConnections++;
          
          // Register user with server
          socket.emit('register', {
            userId: user.userId,
            username: user.username
          });

          // Listen for matches
          socket.on('match', (matchData) => {
            this.matches.push({
              user1: user.userId,
              user2: matchData.userId,
              timestamp: Date.now(),
              distance: matchData.distance
            });
          });

          clearTimeout(timeout);
          this.sockets.push({ socket, user });
          resolve();
        });

        socket.on('error', () => {
          clearTimeout(timeout);
          socket.disconnect();
          resolve();
        });

      } catch (error) {
        resolve();
      }
    });
  }

  // Simulate shaking behavior
  async simulateShaking(shakeInterval, testDuration) {
    const endTime = Date.now() + testDuration;
    
    while (Date.now() < endTime) {
      // Select random users to shake
      const shakingUsers = this.sockets
        .filter(() => Math.random() < 0.3) // 30% chance each user shakes
        .slice(0, Math.floor(this.sockets.length * 0.5)); // Max 50% shake at once

      // Send shake events
      const shakePromises = shakingUsers.map(({ socket, user }) => {
        return this.sendShake(socket, user);
      });

      await Promise.allSettled(shakePromises);
      
      // Wait for next shake interval
      await utils.sleep(shakeInterval);
    }
  }

  // Send shake event for a user
  async sendShake(socket, user) {
    return new Promise((resolve) => {
      try {
        const shakeData = {
          userId: user.userId,
          username: user.username,
          location: user.location,
          maxDistance: 10, // 10km max distance
          timestamp: Date.now(),
          blockedUsers: [],
          rematchEnabled: true,
          minAge: 18,
          maxAge: 100
        };

        socket.emit('shake', shakeData);
        this.completedShakes++;
        
        resolve();
      } catch (error) {
        resolve();
      }
    });
  }

  // Close all socket connections
  async closeAllConnections() {
    const closePromises = this.sockets.map(({ socket }) => {
      return new Promise((resolve) => {
        socket.on('disconnect', resolve);
        socket.disconnect();
        setTimeout(resolve, 1000);
      });
    });

    await Promise.allSettled(closePromises);
    this.sockets = [];
    this.activeConnections = 0;
  }

  // Generate test report
  generateReport(duration, monitoringReport) {
    // Calculate match statistics
    const matchStats = {
      totalMatches: this.matches.length,
      totalShakes: this.completedShakes,
      matchRate: this.completedShakes > 0 ? (this.matches.length / this.completedShakes) * 100 : 0,
      shakesPerSecond: this.completedShakes / (duration / 1000),
      matchesPerSecond: this.matches.length / (duration / 1000)
    };

    // Distance analysis
    const distances = this.matches.map(m => m.distance).filter(d => d);
    const distanceStats = distances.length > 0 ? {
      min: Math.min(...distances),
      max: Math.max(...distances),
      avg: distances.reduce((a, b) => a + b, 0) / distances.length,
      count: distances.length
    } : null;

    // Match time distribution
    const matchTimes = this.matches.map(m => m.timestamp);
    const timeDistribution = {};
    if (matchTimes.length > 0) {
      const startTime = Math.min(...matchTimes);
      const intervals = Math.ceil(duration / 60000); // 1-minute intervals
      
      for (let i = 0; i < intervals; i++) {
        const intervalStart = startTime + (i * 60000);
        const intervalEnd = intervalStart + 60000;
        const count = matchTimes.filter(t => t >= intervalStart && t < intervalEnd).length;
        timeDistribution[`${i + 1}min`] = count;
      }
    }

    return {
      testType: 'matching',
      duration,
      matchStats,
      distanceStats,
      timeDistribution,
      monitoring: monitoringReport,
      thresholds: {
        throughput: matchStats.shakesPerSecond >= config.thresholds.throughput.matching,
        matchRate: matchStats.matchRate >= 5 // At least 5% match rate
      },
      rawMatches: this.matches,
      rawResults: this.results
    };
  }

  // Get current progress
  getProgress() {
    return {
      activeConnections: this.activeConnections,
      completedShakes: this.completedShakes,
      totalMatches: this.matches.length,
      matchRate: this.completedShakes > 0 ? 
        ((this.matches.length / this.completedShakes) * 100).toFixed(2) : 0
    };
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new MatchingLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--duration=')) {
      options.testDuration = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--shake-interval=')) {
      options.shakeInterval = parseInt(arg.split('=')[1]);
    } else if (arg === '--use-existing') {
      options.useExistingUsers = true;
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Matching load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Matching load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = MatchingLoadTest;
