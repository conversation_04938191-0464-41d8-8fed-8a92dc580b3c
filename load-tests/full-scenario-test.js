// Full Scenario Load Test - Simulates realistic user journeys
const io = require('socket.io-client');
const axios = require('axios');
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

class FullScenarioLoadTest {
  constructor() {
    this.results = [];
    this.users = [];
    this.activeConnections = 0;
    this.completedScenarios = 0;
    this.sockets = [];
    this.monitor = new PerformanceMonitor();
    this.scenarios = {
      newUser: 0,
      returningUser: 0,
      activeChatter: 0,
      casualBrowser: 0
    };
  }

  // Run full scenario load test
  async run(options = {}) {
    const {
      totalUsers = config.test.totalUsers,
      testDuration = config.test.testDuration,
      rampUpTime = config.test.rampUpTime
    } = options;

    console.log(`🚀 Starting Full Scenario Load Test`);
    console.log(`📊 Target: ${totalUsers} users with realistic behavior`);
    console.log(`⏱️  Test duration: ${utils.formatDuration(testDuration)}`);
    console.log(`📈 Ramp-up time: ${utils.formatDuration(rampUpTime)}`);
    console.log(`🎯 Server: ${config.getServerUrl()}`);
    console.log('');

    // Start performance monitoring
    this.monitor.start();

    const startTime = Date.now();
    
    try {
      // Generate user scenarios
      console.log('👥 Generating user scenarios...');
      const userScenarios = this.generateUserScenarios(totalUsers);
      console.log(`✅ Generated ${userScenarios.length} user scenarios`);
      this.logScenarioDistribution();

      // Ramp up users gradually
      console.log('\n📈 Ramping up users...');
      await this.rampUpUsers(userScenarios, rampUpTime);

      // Run scenarios for test duration
      console.log('\n🎭 Running user scenarios...');
      await this.runScenarios(testDuration);

      // Graceful shutdown
      console.log('\n🔌 Shutting down connections...');
      await this.shutdownAllConnections();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Stop monitoring and get report
      const monitoringReport = this.monitor.stop();

      // Generate test report
      const report = this.generateReport(duration, monitoringReport);
      
      this.displayResults(report);

      // Save detailed report
      if (config.reporting.enabled) {
        await utils.generateReport('full-scenario-load-test', report, config.reporting.outputDir);
      }

      return report;

    } catch (error) {
      console.error('❌ Full scenario load test failed:', error);
      await this.shutdownAllConnections();
      this.monitor.stop();
      throw error;
    }
  }

  // Generate different user scenarios
  generateUserScenarios(totalUsers) {
    const scenarios = [];
    
    // Define scenario distributions
    const distributions = {
      newUser: 0.3,        // 30% new users (register, setup profile, explore)
      returningUser: 0.4,  // 40% returning users (login, update profile, match)
      activeChatter: 0.2,  // 20% active chatters (heavy messaging)
      casualBrowser: 0.1   // 10% casual browsers (minimal activity)
    };

    let userIndex = 0;
    
    Object.entries(distributions).forEach(([scenarioType, percentage]) => {
      const count = Math.floor(totalUsers * percentage);
      
      for (let i = 0; i < count; i++) {
        scenarios.push({
          id: ++userIndex,
          type: scenarioType,
          user: utils.generateUser(userIndex),
          startDelay: Math.random() * 60000, // Random start within first minute
          behavior: this.generateBehaviorPattern(scenarioType)
        });
        
        this.scenarios[scenarioType]++;
      }
    });

    // Fill remaining slots with random scenarios
    while (scenarios.length < totalUsers) {
      const types = Object.keys(distributions);
      const randomType = types[Math.floor(Math.random() * types.length)];
      
      scenarios.push({
        id: ++userIndex,
        type: randomType,
        user: utils.generateUser(userIndex),
        startDelay: Math.random() * 60000,
        behavior: this.generateBehaviorPattern(randomType)
      });
      
      this.scenarios[randomType]++;
    }

    return scenarios;
  }

  // Generate behavior pattern for each scenario type
  generateBehaviorPattern(scenarioType) {
    const patterns = {
      newUser: {
        actions: ['register', 'setupProfile', 'exploreApp', 'firstShake', 'casualMessaging'],
        actionIntervals: [0, 30000, 60000, 120000, 180000], // 0s, 30s, 1m, 2m, 3m
        sessionDuration: 300000, // 5 minutes
        shakeFrequency: 0.3, // 30% chance to shake when prompted
        messageFrequency: 0.2 // 20% chance to send messages
      },
      returningUser: {
        actions: ['login', 'updateProfile', 'activeShaking', 'messaging', 'browsing'],
        actionIntervals: [0, 15000, 45000, 90000, 150000], // More frequent actions
        sessionDuration: 600000, // 10 minutes
        shakeFrequency: 0.7, // 70% chance to shake
        messageFrequency: 0.5 // 50% chance to send messages
      },
      activeChatter: {
        actions: ['login', 'heavyMessaging', 'continuousShaking', 'profileUpdates'],
        actionIntervals: [0, 10000, 30000, 60000], // Very frequent
        sessionDuration: 900000, // 15 minutes
        shakeFrequency: 0.9, // 90% chance to shake
        messageFrequency: 0.8 // 80% chance to send messages
      },
      casualBrowser: {
        actions: ['login', 'lightBrowsing', 'occasionalShake'],
        actionIntervals: [0, 120000, 300000], // Infrequent actions
        sessionDuration: 180000, // 3 minutes
        shakeFrequency: 0.1, // 10% chance to shake
        messageFrequency: 0.05 // 5% chance to send messages
      }
    };

    return patterns[scenarioType];
  }

  // Ramp up users gradually
  async rampUpUsers(userScenarios, rampUpTime) {
    const batchSize = Math.max(1, Math.floor(userScenarios.length / 20)); // 20 batches
    const batchDelay = rampUpTime / 20;
    
    const batches = utils.batchArray(userScenarios, batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      // Start batch scenarios
      const batchPromises = batch.map(scenario => this.startUserScenario(scenario));
      await Promise.allSettled(batchPromises);
      
      const progress = ((i + 1) / batches.length * 100).toFixed(1);
      console.log(`📈 Ramp-up progress: ${progress}% (${this.activeConnections} active users)`);
      
      if (i < batches.length - 1) {
        await utils.sleep(batchDelay);
      }
    }
    
    console.log(`✅ Ramp-up complete: ${this.activeConnections} users active`);
  }

  // Start a user scenario
  async startUserScenario(scenario) {
    // Wait for start delay
    await utils.sleep(scenario.startDelay);
    
    try {
      // Execute scenario based on type
      switch (scenario.type) {
        case 'newUser':
          await this.executeNewUserScenario(scenario);
          break;
        case 'returningUser':
          await this.executeReturningUserScenario(scenario);
          break;
        case 'activeChatter':
          await this.executeActiveChatterScenario(scenario);
          break;
        case 'casualBrowser':
          await this.executeCasualBrowserScenario(scenario);
          break;
      }
      
      this.completedScenarios++;
      
    } catch (error) {
      this.recordScenarioResult(scenario, false, error.message);
    }
  }

  // Execute new user scenario
  async executeNewUserScenario(scenario) {
    const startTime = Date.now();
    
    try {
      // Register user
      await this.registerUser(scenario.user);
      await utils.sleep(5000);
      
      // Login and connect
      const loginResult = await this.loginUser(scenario.user);
      if (!loginResult.success) throw new Error('Login failed');
      
      scenario.user.userId = loginResult.userId;
      const socket = await this.connectUser(scenario.user);
      
      // Setup profile
      await utils.sleep(10000);
      await this.updateUserProfile(scenario.user);
      
      // Explore app with occasional shakes and messages
      const sessionEnd = Date.now() + scenario.behavior.sessionDuration;
      
      while (Date.now() < sessionEnd) {
        // Random shake
        if (Math.random() < scenario.behavior.shakeFrequency) {
          await this.sendShake(socket, scenario.user);
        }
        
        // Random message (if matched)
        if (Math.random() < scenario.behavior.messageFrequency) {
          await this.sendRandomMessage(socket, scenario.user);
        }
        
        await utils.sleep(30000); // 30 second intervals
      }
      
      socket.disconnect();
      this.activeConnections--;
      
      this.recordScenarioResult(scenario, true, null, Date.now() - startTime);
      
    } catch (error) {
      this.recordScenarioResult(scenario, false, error.message);
    }
  }

  // Execute returning user scenario
  async executeReturningUserScenario(scenario) {
    const startTime = Date.now();
    
    try {
      // Register first (simulating existing user)
      await this.registerUser(scenario.user);
      
      // Login and connect
      const loginResult = await this.loginUser(scenario.user);
      if (!loginResult.success) throw new Error('Login failed');
      
      scenario.user.userId = loginResult.userId;
      const socket = await this.connectUser(scenario.user);
      
      // Active session with frequent shakes and messages
      const sessionEnd = Date.now() + scenario.behavior.sessionDuration;
      
      while (Date.now() < sessionEnd) {
        // Frequent shakes
        if (Math.random() < scenario.behavior.shakeFrequency) {
          await this.sendShake(socket, scenario.user);
        }
        
        // Regular messaging
        if (Math.random() < scenario.behavior.messageFrequency) {
          await this.sendRandomMessage(socket, scenario.user);
        }
        
        await utils.sleep(15000); // 15 second intervals
      }
      
      socket.disconnect();
      this.activeConnections--;
      
      this.recordScenarioResult(scenario, true, null, Date.now() - startTime);
      
    } catch (error) {
      this.recordScenarioResult(scenario, false, error.message);
    }
  }

  // Execute active chatter scenario
  async executeActiveChatterScenario(scenario) {
    const startTime = Date.now();
    
    try {
      // Quick setup
      await this.registerUser(scenario.user);
      const loginResult = await this.loginUser(scenario.user);
      if (!loginResult.success) throw new Error('Login failed');
      
      scenario.user.userId = loginResult.userId;
      const socket = await this.connectUser(scenario.user);
      
      // Very active session
      const sessionEnd = Date.now() + scenario.behavior.sessionDuration;
      
      while (Date.now() < sessionEnd) {
        // Very frequent shakes
        if (Math.random() < scenario.behavior.shakeFrequency) {
          await this.sendShake(socket, scenario.user);
        }
        
        // Heavy messaging
        if (Math.random() < scenario.behavior.messageFrequency) {
          await this.sendRandomMessage(socket, scenario.user);
          // Send multiple messages
          if (Math.random() < 0.5) {
            await utils.sleep(2000);
            await this.sendRandomMessage(socket, scenario.user);
          }
        }
        
        await utils.sleep(5000); // 5 second intervals
      }
      
      socket.disconnect();
      this.activeConnections--;
      
      this.recordScenarioResult(scenario, true, null, Date.now() - startTime);
      
    } catch (error) {
      this.recordScenarioResult(scenario, false, error.message);
    }
  }

  // Execute casual browser scenario
  async executeCasualBrowserScenario(scenario) {
    const startTime = Date.now();
    
    try {
      // Minimal activity
      await this.registerUser(scenario.user);
      const loginResult = await this.loginUser(scenario.user);
      if (!loginResult.success) throw new Error('Login failed');
      
      scenario.user.userId = loginResult.userId;
      const socket = await this.connectUser(scenario.user);
      
      // Light browsing session
      const sessionEnd = Date.now() + scenario.behavior.sessionDuration;
      
      while (Date.now() < sessionEnd) {
        // Occasional shake
        if (Math.random() < scenario.behavior.shakeFrequency) {
          await this.sendShake(socket, scenario.user);
        }
        
        await utils.sleep(60000); // 1 minute intervals
      }
      
      socket.disconnect();
      this.activeConnections--;
      
      this.recordScenarioResult(scenario, true, null, Date.now() - startTime);
      
    } catch (error) {
      this.recordScenarioResult(scenario, false, error.message);
    }
  }

  // Helper methods (register, login, connect, etc.)
  async registerUser(user) {
    const response = await axios.post(config.getApiUrl('/register'), {
      username: user.username,
      password: user.password
    }, { timeout: 10000 });
    
    return response.status === 201;
  }

  async loginUser(user) {
    try {
      const response = await axios.post(config.getApiUrl('/login'), {
        username: user.username,
        password: user.password
      }, { timeout: 10000 });
      
      return {
        success: true,
        userId: response.data.userId
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async connectUser(user) {
    return new Promise((resolve, reject) => {
      const socket = io(config.getSocketUrl(), {
        timeout: 10000,
        forceNew: true
      });

      const timeout = setTimeout(() => {
        socket.disconnect();
        reject(new Error('Connection timeout'));
      }, 15000);

      socket.on('connect', () => {
        this.activeConnections++;
        socket.emit('register', {
          userId: user.userId,
          username: user.username
        });
        
        clearTimeout(timeout);
        this.sockets.push(socket);
        resolve(socket);
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  async updateUserProfile(user) {
    try {
      await axios.put(config.getApiUrl(`/profile/${user.userId}`), {
        age: user.age,
        description: user.description,
        passions: user.passions,
        images: user.images
      }, { timeout: 15000 });
    } catch (error) {
      // Profile update failure is not critical for scenario
    }
  }

  async sendShake(socket, user) {
    try {
      socket.emit('shake', {
        userId: user.userId,
        username: user.username,
        location: user.location,
        maxDistance: 10,
        timestamp: Date.now(),
        blockedUsers: [],
        rematchEnabled: true,
        minAge: 18,
        maxAge: 100
      });
    } catch (error) {
      // Shake failure is not critical
    }
  }

  async sendRandomMessage(socket, user) {
    try {
      // Create a dummy receiver (in real scenario, this would be a matched user)
      const messageData = {
        id: utils.generateMessageId(),
        senderId: user.userId,
        senderUsername: user.username,
        receiverId: 'dummy_receiver',
        receiverUsername: 'dummy_user',
        text: utils.generateMessage(),
        timestamp: Date.now()
      };
      
      socket.emit('sendMessage', messageData);
    } catch (error) {
      // Message failure is not critical
    }
  }

  recordScenarioResult(scenario, success, error, duration) {
    this.results.push({
      scenarioId: scenario.id,
      scenarioType: scenario.type,
      userId: scenario.user.id,
      success,
      error,
      duration,
      timestamp: Date.now()
    });
  }

  async runScenarios(testDuration) {
    // Scenarios are already running, just wait for test duration
    await utils.sleep(testDuration);
  }

  async shutdownAllConnections() {
    const closePromises = this.sockets.map(socket => {
      return new Promise((resolve) => {
        socket.on('disconnect', resolve);
        socket.disconnect();
        setTimeout(resolve, 1000);
      });
    });

    await Promise.allSettled(closePromises);
    this.sockets = [];
    this.activeConnections = 0;
  }

  logScenarioDistribution() {
    console.log('📊 Scenario Distribution:');
    Object.entries(this.scenarios).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} users`);
    });
  }

  generateReport(duration, monitoringReport) {
    const summary = utils.calculateSummary(this.results);
    
    // Scenario-specific statistics
    const scenarioStats = {};
    Object.keys(this.scenarios).forEach(type => {
      const scenarioResults = this.results.filter(r => r.scenarioType === type);
      scenarioStats[type] = {
        total: scenarioResults.length,
        successful: scenarioResults.filter(r => r.success).length,
        failed: scenarioResults.filter(r => !r.success).length,
        avgDuration: scenarioResults.length > 0 ? 
          scenarioResults.reduce((sum, r) => sum + (r.duration || 0), 0) / scenarioResults.length : 0
      };
    });

    return {
      testType: 'full-scenario',
      duration,
      summary,
      scenarioStats,
      scenarioDistribution: this.scenarios,
      monitoring: monitoringReport,
      rawResults: this.results
    };
  }

  displayResults(report) {
    console.log('\n📊 Full Scenario Load Test Results:');
    console.log(`⏱️  Duration: ${utils.formatDuration(report.duration)}`);
    console.log(`🎭 Completed Scenarios: ${this.completedScenarios}`);
    console.log(`✅ Successful: ${report.summary.successes}`);
    console.log(`❌ Failed: ${report.summary.errors}`);
    console.log(`📈 Success Rate: ${(100 - report.summary.errorRate).toFixed(2)}%`);
    
    console.log('\n📊 Scenario Performance:');
    Object.entries(report.scenarioStats).forEach(([type, stats]) => {
      console.log(`  ${type}: ${stats.successful}/${stats.total} successful (${utils.formatDuration(stats.avgDuration)} avg)`);
    });
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new FullScenarioLoadTest();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--duration=')) {
      options.testDuration = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--ramp-up=')) {
      options.rampUpTime = parseInt(arg.split('=')[1]);
    }
  });

  test.run(options)
    .then(report => {
      console.log('\n✅ Full scenario load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Full scenario load test failed:', error.message);
      process.exit(1);
    });
}

module.exports = FullScenarioLoadTest;
