# Shake2 Load Testing Suite

A comprehensive load testing suite designed to test the Shake2 dating app with up to 50,000 concurrent users performing various operations including registration, profile updates, socket connections, matching, and messaging.

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v14 or higher)
2. **Server running** - Your Shake2 server should be running and accessible
3. **Dependencies installed**:
   ```bash
   npm install
   ```

### Basic Usage

Run the complete load test suite:
```bash
npm run load-test
```

Run individual tests:
```bash
# Registration test
npm run load-test:registration

# Profile updates test
npm run load-test:profiles

# Socket connections test
npm run load-test:sockets

# Matching system test
npm run load-test:matching

# Messaging test
npm run load-test:messaging

# Full realistic scenario test
npm run load-test:full
```

## 📊 Test Types

### 1. Registration Load Test
Tests concurrent user registration with realistic data generation.
- **Target**: 10,000 registrations (configurable)
- **Features**: Username generation, password hashing, duplicate handling
- **Metrics**: Success rate, response times, throughput

### 2. Profile Update Load Test
Tests concurrent profile updates including image uploads.
- **Target**: 30,000 profile updates (configurable)
- **Features**: Age, description, passions, image uploads
- **Metrics**: Update success rate, image upload performance

### 3. Socket Connection Load Test
Tests WebSocket connections and real-time communication.
- **Target**: 45,000 concurrent connections (configurable)
- **Features**: Connection establishment, user registration, keep-alive
- **Metrics**: Connection success rate, concurrent users, stability

### 4. Matching System Load Test
Tests the shake-to-match functionality with location-based matching.
- **Target**: 40,000 users shaking for matches (configurable)
- **Features**: Location simulation, shake events, match generation
- **Metrics**: Match rate, shake throughput, location accuracy

### 5. Messaging Load Test
Tests real-time messaging between matched users.
- **Target**: 20,000 users sending messages (configurable)
- **Features**: Message sending, delivery, user pairs
- **Metrics**: Message throughput, delivery success rate

### 6. Full Scenario Test
Simulates realistic user journeys with different behavior patterns.
- **Scenarios**: New users, returning users, active chatters, casual browsers
- **Features**: Complete user lifecycle, realistic timing, varied behavior
- **Metrics**: Scenario completion rates, user engagement patterns

## ⚙️ Configuration

### Environment Variables

```bash
# Server configuration
SERVER_HOST=**************
SERVER_PORT=3000

# Test configuration
TOTAL_USERS=50000
RAMP_UP_TIME=300000    # 5 minutes
TEST_DURATION=600000   # 10 minutes
BATCH_SIZE=100
BATCH_DELAY=1000

# Database configuration
MONGODB_URI=mongodb://**************:27017/shakeAndMatch
```

### Configuration File

Edit `load-tests/config.js` to customize:

```javascript
const config = {
  test: {
    totalUsers: 50000,
    rampUpTime: 300000,  // 5 minutes
    testDuration: 600000, // 10 minutes
    batchSize: 100,
    batchDelay: 1000
  },
  
  scenarios: {
    registration: {
      enabled: true,
      percentage: 20,
      concurrent: 1000
    },
    // ... other scenarios
  },
  
  thresholds: {
    responseTime: {
      p95: 2000,  // 95th percentile under 2s
      p99: 5000   // 99th percentile under 5s
    },
    errorRate: 0.05  // Max 5% error rate
  }
};
```

## 🎯 Command Line Options

### Orchestrator (Main Test Suite)
```bash
node load-tests/orchestrator.js --users=50000 --skip=registration,profiles --parallel
```

Options:
- `--users=N`: Total number of users (default: 50000)
- `--skip=test1,test2`: Skip specific tests
- `--parallel`: Run tests in parallel (faster but may affect results)

### Individual Tests
```bash
# Registration test
node load-tests/registration-test.js --users=10000 --batch-size=200 --batch-delay=500

# Profile test
node load-tests/profile-test.js --users=30000 --use-existing

# Socket test
node load-tests/socket-test.js --users=45000 --keep-alive=300000

# Matching test
node load-tests/matching-test.js --users=40000 --duration=600000 --shake-interval=10000

# Messaging test
node load-tests/messaging-test.js --users=20000 --messages=5 --interval=5000

# Full scenario test
node load-tests/full-scenario-test.js --users=50000 --duration=900000 --ramp-up=300000
```

## 📈 Performance Monitoring

The test suite includes real-time performance monitoring:

### System Metrics
- CPU usage and load average
- Memory usage and availability
- Network performance

### Server Metrics
- Response times
- Active user count
- Server availability
- Error rates

### Database Metrics
- Connection status
- Operation performance

### Real-time Display
```
📊 [2024-01-15T10:30:00.000Z] CPU: 2.45 | MEM: 67.3% | Server: online (45ms) | Users: 12,543
```

## 📊 Reports and Results

### Report Formats
- **JSON**: Detailed machine-readable results
- **Console**: Human-readable summary
- **CSV**: Data for spreadsheet analysis

### Report Location
Reports are saved to `./load-tests/reports/` with timestamps:
- `comprehensive-load-test_2024-01-15T10-30-00-000Z.json`
- `registration-load-test_2024-01-15T10-35-00-000Z.json`

### Key Metrics
- **Success Rate**: Percentage of successful operations
- **Response Times**: Min, max, average, P95, P99
- **Throughput**: Operations per second
- **Error Analysis**: Error types and frequencies
- **Resource Usage**: CPU, memory, network utilization

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: ECONNREFUSED
   ```
   - Check if server is running
   - Verify server host/port configuration
   - Check firewall settings

2. **High Error Rates**
   - Reduce batch size or increase delays
   - Check server resource limits
   - Monitor database performance

3. **Memory Issues**
   ```
   Error: JavaScript heap out of memory
   ```
   - Reduce concurrent users
   - Increase Node.js memory limit: `node --max-old-space-size=8192`

4. **Socket Connection Limits**
   - Check system ulimit settings
   - Increase file descriptor limits
   - Use connection pooling

### Performance Tuning

1. **For Higher Throughput**:
   - Increase batch sizes
   - Reduce batch delays
   - Use parallel execution

2. **For Stability**:
   - Decrease batch sizes
   - Increase delays between operations
   - Enable gradual ramp-up

3. **For Accuracy**:
   - Run tests sequentially
   - Use longer test durations
   - Enable detailed monitoring

## 📋 Test Scenarios

### Realistic Load Distribution
- **20%** New user registrations
- **60%** Profile updates and maintenance
- **90%** Active socket connections
- **80%** Users participating in matching
- **40%** Active messaging users

### User Behavior Patterns
- **New Users**: Register → Setup Profile → Explore → Light Activity
- **Returning Users**: Login → Active Matching → Regular Messaging
- **Active Chatters**: Heavy messaging, frequent shaking, long sessions
- **Casual Browsers**: Minimal activity, short sessions

## 🎯 Success Criteria

### Performance Thresholds
- **Response Time**: P95 < 2s, P99 < 5s
- **Error Rate**: < 5%
- **Throughput**: 
  - Registration: > 100/s
  - Profile Updates: > 200/s
  - Socket Connections: > 500/s
  - Matching: > 300/s
  - Messaging: > 400/s

### Scalability Goals
- Support 50,000 concurrent users
- Maintain performance under load
- Graceful degradation under stress
- Quick recovery from failures

## 🚨 Safety Features

- **Gradual Ramp-up**: Prevents server overload
- **Batch Processing**: Controlled load application
- **Error Handling**: Graceful failure management
- **Resource Monitoring**: Real-time system health
- **Graceful Shutdown**: Clean connection termination

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review server logs during test execution
3. Monitor system resources
4. Adjust configuration parameters as needed

---

**Note**: Always test in a staging environment before running against production systems. These tests generate significant load and should be used responsibly.
