// Load Test Orchestrator - Coordinates all load tests
const config = require('./config');
const utils = require('./utils');
const PerformanceMonitor = require('./performance-monitor');

// Import test classes
const RegistrationLoadTest = require('./registration-test');
const ProfileUpdateLoadTest = require('./profile-test');
const SocketConnectionLoadTest = require('./socket-test');
const MatchingLoadTest = require('./matching-test');
const MessagingLoadTest = require('./messaging-test');

class LoadTestOrchestrator {
  constructor() {
    this.results = {};
    this.overallMonitor = new PerformanceMonitor();
    this.startTime = null;
    this.endTime = null;
  }

  // Run all load tests in sequence
  async runAll(options = {}) {
    const {
      totalUsers = config.test.totalUsers,
      skipTests = [],
      runParallel = false
    } = options;

    console.log('🚀 Starting Comprehensive Load Test Suite');
    console.log(`📊 Target: ${totalUsers} total users`);
    console.log(`🎯 Server: ${config.getServerUrl()}`);
    console.log(`⏱️  Estimated duration: ${utils.formatDuration(this.estimateDuration(totalUsers))}`);
    console.log('');

    // Validate configuration
    config.validate();

    // Start overall monitoring
    this.overallMonitor.start();
    this.startTime = Date.now();

    try {
      if (runParallel) {
        await this.runTestsInParallel(totalUsers, skipTests);
      } else {
        await this.runTestsInSequence(totalUsers, skipTests);
      }

      this.endTime = Date.now();
      const overallMonitoring = this.overallMonitor.stop();

      // Generate comprehensive report
      const report = await this.generateComprehensiveReport(overallMonitoring);
      
      // Display summary
      this.displaySummary(report);

      return report;

    } catch (error) {
      console.error('❌ Load test suite failed:', error);
      this.overallMonitor.stop();
      throw error;
    }
  }

  // Run tests in sequence (recommended for accurate results)
  async runTestsInSequence(totalUsers, skipTests) {
    const testSequence = [
      { name: 'registration', class: RegistrationLoadTest, users: Math.floor(totalUsers * 0.2) },
      { name: 'profileUpdate', class: ProfileUpdateLoadTest, users: Math.floor(totalUsers * 0.6) },
      { name: 'socketConnection', class: SocketConnectionLoadTest, users: Math.floor(totalUsers * 0.9) },
      { name: 'matching', class: MatchingLoadTest, users: Math.floor(totalUsers * 0.8) },
      { name: 'messaging', class: MessagingLoadTest, users: Math.floor(totalUsers * 0.4) }
    ];

    for (const testConfig of testSequence) {
      if (skipTests.includes(testConfig.name)) {
        console.log(`⏭️  Skipping ${testConfig.name} test`);
        continue;
      }

      if (!config.scenarios[testConfig.name]?.enabled) {
        console.log(`⏭️  ${testConfig.name} test disabled in config`);
        continue;
      }

      console.log(`\n🔄 Running ${testConfig.name} test...`);
      
      try {
        const testInstance = new testConfig.class();
        const result = await testInstance.run({
          totalUsers: testConfig.users,
          useExistingUsers: testConfig.name !== 'registration'
        });
        
        this.results[testConfig.name] = result;
        console.log(`✅ ${testConfig.name} test completed`);
        
        // Brief pause between tests
        await utils.sleep(5000);
        
      } catch (error) {
        console.error(`❌ ${testConfig.name} test failed:`, error.message);
        this.results[testConfig.name] = {
          error: error.message,
          failed: true
        };
      }
    }
  }

  // Run tests in parallel (faster but may affect results)
  async runTestsInParallel(totalUsers, skipTests) {
    const testConfigs = [
      { name: 'registration', class: RegistrationLoadTest, users: Math.floor(totalUsers * 0.2) },
      { name: 'profileUpdate', class: ProfileUpdateLoadTest, users: Math.floor(totalUsers * 0.6) },
      { name: 'socketConnection', class: SocketConnectionLoadTest, users: Math.floor(totalUsers * 0.9) },
      { name: 'matching', class: MatchingLoadTest, users: Math.floor(totalUsers * 0.8) },
      { name: 'messaging', class: MessagingLoadTest, users: Math.floor(totalUsers * 0.4) }
    ];

    const enabledTests = testConfigs.filter(test => 
      !skipTests.includes(test.name) && 
      config.scenarios[test.name]?.enabled
    );

    console.log(`\n🔄 Running ${enabledTests.length} tests in parallel...`);

    const testPromises = enabledTests.map(async (testConfig) => {
      try {
        const testInstance = new testConfig.class();
        const result = await testInstance.run({
          totalUsers: testConfig.users,
          useExistingUsers: testConfig.name !== 'registration'
        });
        
        return { name: testConfig.name, result };
      } catch (error) {
        return { 
          name: testConfig.name, 
          result: { error: error.message, failed: true } 
        };
      }
    });

    const results = await Promise.allSettled(testPromises);
    
    results.forEach((result, index) => {
      const testName = enabledTests[index].name;
      if (result.status === 'fulfilled') {
        this.results[testName] = result.value.result;
        console.log(`✅ ${testName} test completed`);
      } else {
        this.results[testName] = { error: result.reason.message, failed: true };
        console.error(`❌ ${testName} test failed:`, result.reason.message);
      }
    });
  }

  // Generate comprehensive report
  async generateComprehensiveReport(overallMonitoring) {
    const duration = this.endTime - this.startTime;
    
    // Calculate overall statistics
    const overallStats = this.calculateOverallStats();
    
    // Check thresholds
    const thresholdResults = this.checkThresholds();
    
    // Generate recommendations
    const recommendations = this.generateRecommendations();

    const report = {
      timestamp: new Date().toISOString(),
      duration,
      overallStats,
      thresholdResults,
      recommendations,
      individualTests: this.results,
      monitoring: overallMonitoring,
      configuration: {
        totalUsers: config.test.totalUsers,
        server: config.getServerUrl(),
        scenarios: config.scenarios
      }
    };

    // Save comprehensive report
    if (config.reporting.enabled) {
      await utils.generateReport('comprehensive-load-test', report, config.reporting.outputDir);
    }

    return report;
  }

  // Calculate overall statistics across all tests
  calculateOverallStats() {
    const stats = {
      totalTests: Object.keys(this.results).length,
      passedTests: 0,
      failedTests: 0,
      totalRequests: 0,
      totalErrors: 0,
      avgResponseTime: 0,
      maxResponseTime: 0,
      totalThroughput: 0
    };

    const responseTimes = [];
    
    Object.values(this.results).forEach(result => {
      if (result.failed) {
        stats.failedTests++;
        return;
      }
      
      stats.passedTests++;
      
      if (result.summary) {
        stats.totalRequests += result.summary.total || 0;
        stats.totalErrors += result.summary.errors || 0;
        stats.totalThroughput += result.summary.throughput || 0;
        
        if (result.summary.responseTime) {
          responseTimes.push(result.summary.responseTime.avg);
          stats.maxResponseTime = Math.max(stats.maxResponseTime, result.summary.responseTime.max || 0);
        }
      }
    });

    if (responseTimes.length > 0) {
      stats.avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    }

    stats.overallErrorRate = stats.totalRequests > 0 ? 
      (stats.totalErrors / stats.totalRequests) * 100 : 0;

    return stats;
  }

  // Check if tests meet defined thresholds
  checkThresholds() {
    const results = {};
    
    Object.entries(this.results).forEach(([testName, result]) => {
      if (result.failed) {
        results[testName] = { passed: false, reason: 'Test failed to complete' };
        return;
      }
      
      if (!result.thresholds) {
        results[testName] = { passed: true, reason: 'No thresholds defined' };
        return;
      }
      
      const failures = [];
      
      Object.entries(result.thresholds).forEach(([metric, passed]) => {
        if (typeof passed === 'boolean' && !passed) {
          failures.push(metric);
        } else if (typeof passed === 'object') {
          Object.entries(passed).forEach(([subMetric, subPassed]) => {
            if (!subPassed) {
              failures.push(`${metric}.${subMetric}`);
            }
          });
        }
      });
      
      results[testName] = {
        passed: failures.length === 0,
        failures: failures.length > 0 ? failures : undefined
      };
    });
    
    return results;
  }

  // Generate performance recommendations
  generateRecommendations() {
    const recommendations = [];
    const overallStats = this.calculateOverallStats();
    
    // Error rate recommendations
    if (overallStats.overallErrorRate > 5) {
      recommendations.push({
        type: 'error_rate',
        severity: 'high',
        message: `High error rate (${overallStats.overallErrorRate.toFixed(2)}%). Consider increasing server resources or optimizing code.`
      });
    }
    
    // Response time recommendations
    if (overallStats.avgResponseTime > 2000) {
      recommendations.push({
        type: 'response_time',
        severity: 'medium',
        message: `Average response time is high (${overallStats.avgResponseTime.toFixed(2)}ms). Consider database optimization or caching.`
      });
    }
    
    // Throughput recommendations
    if (overallStats.totalThroughput < 100) {
      recommendations.push({
        type: 'throughput',
        severity: 'medium',
        message: `Low throughput (${overallStats.totalThroughput.toFixed(2)} req/s). Consider horizontal scaling or load balancing.`
      });
    }
    
    // Test-specific recommendations
    Object.entries(this.results).forEach(([testName, result]) => {
      if (result.failed) {
        recommendations.push({
          type: 'test_failure',
          severity: 'high',
          test: testName,
          message: `${testName} test failed. Check server logs and configuration.`
        });
      }
    });
    
    return recommendations;
  }

  // Display summary results
  displaySummary(report) {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE LOAD TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`⏱️  Total Duration: ${utils.formatDuration(report.duration)}`);
    console.log(`🧪 Tests Run: ${report.overallStats.totalTests}`);
    console.log(`✅ Passed: ${report.overallStats.passedTests}`);
    console.log(`❌ Failed: ${report.overallStats.failedTests}`);
    console.log(`📊 Total Requests: ${report.overallStats.totalRequests.toLocaleString()}`);
    console.log(`🚨 Total Errors: ${report.overallStats.totalErrors.toLocaleString()}`);
    console.log(`📈 Error Rate: ${report.overallStats.overallErrorRate.toFixed(2)}%`);
    console.log(`⚡ Total Throughput: ${report.overallStats.totalThroughput.toFixed(2)} req/s`);
    console.log(`📊 Avg Response Time: ${report.overallStats.avgResponseTime.toFixed(2)}ms`);
    
    console.log('\n📋 Individual Test Results:');
    Object.entries(this.results).forEach(([testName, result]) => {
      const status = result.failed ? '❌' : '✅';
      const summary = result.failed ? 'FAILED' : 
        `${result.summary?.successes || 0} success, ${result.summary?.errors || 0} errors`;
      console.log(`  ${status} ${testName}: ${summary}`);
    });
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        const icon = rec.severity === 'high' ? '🚨' : rec.severity === 'medium' ? '⚠️' : 'ℹ️';
        console.log(`  ${icon} ${rec.message}`);
      });
    }
    
    console.log('\n' + '='.repeat(60));
  }

  // Estimate test duration
  estimateDuration(totalUsers) {
    // Rough estimation based on test complexity and user count
    const baseTime = 60000; // 1 minute base
    const userFactor = totalUsers * 10; // 10ms per user
    const testCount = Object.values(config.scenarios).filter(s => s.enabled).length;
    
    return baseTime + userFactor + (testCount * 30000); // 30s per test
  }
}

// Run orchestrator if called directly
if (require.main === module) {
  const orchestrator = new LoadTestOrchestrator();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--users=')) {
      options.totalUsers = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--skip=')) {
      options.skipTests = arg.split('=')[1].split(',');
    } else if (arg === '--parallel') {
      options.runParallel = true;
    }
  });

  orchestrator.runAll(options)
    .then(report => {
      console.log('\n🎉 Load test suite completed successfully!');
      
      // Exit with appropriate code
      const hasFailures = report.overallStats.failedTests > 0 || 
        Object.values(report.thresholdResults).some(r => !r.passed);
      process.exit(hasFailures ? 1 : 0);
    })
    .catch(error => {
      console.error('\n💥 Load test suite failed:', error.message);
      process.exit(1);
    });
}

module.exports = LoadTestOrchestrator;
