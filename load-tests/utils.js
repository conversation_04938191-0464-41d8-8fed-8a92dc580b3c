// Load Testing Utilities
const { faker } = require('@faker-js/faker');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class LoadTestUtils {
  constructor() {
    this.userCounter = 0;
    this.messageCounter = 0;
    this.generatedUsers = new Map();
  }

  // Generate realistic user data
  generateUser(index = null) {
    const userId = index !== null ? index : ++this.userCounter;
    const username = `loadtest_user_${userId}_${Date.now()}`;
    const password = 'LoadTest123!';
    
    const user = {
      id: userId,
      username,
      password,
      age: faker.number.int({ min: 18, max: 65 }),
      description: faker.lorem.sentences(2),
      passions: this.generatePassions(),
      images: this.generateImageData(),
      location: this.generateLocation(),
      createdAt: new Date()
    };
    
    this.generatedUsers.set(userId, user);
    return user;
  }

  // Generate random passions (3-6 passions)
  generatePassions() {
    const allPassions = [
      'Travel', 'Photography', 'Music', 'Sports', 'Reading', 'Cooking',
      'Dancing', 'Hiking', 'Gaming', 'Art', 'Movies', 'Fitness',
      'Technology', 'Fashion', 'Food', 'Nature', 'Animals', 'Writing',
      'Yoga', 'Swimming', 'Running', 'Cycling', 'Meditation', 'Learning'
    ];
    
    const count = faker.number.int({ min: 3, max: 6 });
    return faker.helpers.arrayElements(allPassions, count);
  }

  // Generate fake image data (base64 encoded small images)
  generateImageData() {
    const imageCount = faker.number.int({ min: 1, max: 4 });
    const images = [];
    
    for (let i = 0; i < imageCount; i++) {
      // Generate a small base64 encoded image placeholder
      const imageData = this.generateBase64Image(100, 100);
      images.push(imageData);
    }
    
    return images;
  }

  // Generate a small base64 encoded image
  generateBase64Image(width = 100, height = 100) {
    // Create a simple colored rectangle as base64
    const canvas = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${faker.internet.color()}"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="12">
        Test Image
      </text>
    </svg>`;
    
    return `data:image/svg+xml;base64,${Buffer.from(canvas).toString('base64')}`;
  }

  // Generate random location within a radius
  generateLocation(centerLat = 40.7128, centerLng = -74.0060, radiusKm = 50) {
    const radiusInDegrees = radiusKm / 111.32; // Rough conversion
    
    const u = Math.random();
    const v = Math.random();
    const w = radiusInDegrees * Math.sqrt(u);
    const t = 2 * Math.PI * v;
    const x = w * Math.cos(t);
    const y = w * Math.sin(t);
    
    return {
      latitude: centerLat + x,
      longitude: centerLng + y
    };
  }

  // Generate random message
  generateMessage() {
    const messages = [
      "Hey there! How's your day going?",
      "Nice to match with you!",
      "What are you up to today?",
      "Love your profile! 😊",
      "Want to chat?",
      "Hope you're having a great day!",
      "Thanks for the match!",
      "What's your favorite hobby?",
      "Nice to meet you!",
      "How's the weather where you are?"
    ];
    
    return faker.helpers.arrayElement(messages);
  }

  // Generate unique message ID
  generateMessageId() {
    return `msg_${++this.messageCounter}_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
  }

  // Sleep utility
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Random delay between min and max
  randomDelay(min = 1000, max = 5000) {
    const delay = faker.number.int({ min, max });
    return this.sleep(delay);
  }

  // Batch array into chunks
  batchArray(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  // Generate test report
  async generateReport(testName, results, outputDir) {
    try {
      await fs.mkdir(outputDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${testName}_${timestamp}.json`;
      const filepath = path.join(outputDir, filename);
      
      const report = {
        testName,
        timestamp: new Date().toISOString(),
        results,
        summary: this.calculateSummary(results)
      };
      
      await fs.writeFile(filepath, JSON.stringify(report, null, 2));
      console.log(`Report saved to: ${filepath}`);
      
      return filepath;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  // Calculate test summary statistics
  calculateSummary(results) {
    if (!results || results.length === 0) {
      return { error: 'No results to summarize' };
    }

    const responseTimes = results
      .filter(r => r.responseTime)
      .map(r => r.responseTime)
      .sort((a, b) => a - b);

    const errors = results.filter(r => r.error || r.status >= 400);
    const successes = results.filter(r => !r.error && r.status < 400);

    return {
      total: results.length,
      successes: successes.length,
      errors: errors.length,
      errorRate: (errors.length / results.length) * 100,
      responseTime: {
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes),
        avg: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        p50: this.percentile(responseTimes, 0.5),
        p95: this.percentile(responseTimes, 0.95),
        p99: this.percentile(responseTimes, 0.99)
      },
      throughput: results.length / ((Math.max(...results.map(r => r.timestamp)) - Math.min(...results.map(r => r.timestamp))) / 1000)
    };
  }

  // Calculate percentile
  percentile(arr, p) {
    if (arr.length === 0) return 0;
    const index = Math.ceil(arr.length * p) - 1;
    return arr[index];
  }

  // Format bytes
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Format duration
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

module.exports = new LoadTestUtils();
