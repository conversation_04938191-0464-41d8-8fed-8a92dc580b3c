// Update MatchScreen.js to fetch and display profile information
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Image, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const MatchScreen = ({ route, navigation, serverAddress }) => {
  const { match } = route.params;
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const [matchProfile, setMatchProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
    
    // Fetch match profile data
    fetchMatchProfile();
  }, []);
  
  const fetchMatchProfile = async () => {
    try {
      // First check if we have cached profile data
      const cachedProfile = await AsyncStorage.getItem(`profile_${match.userId}`);
      
      if (cachedProfile) {
        setMatchProfile(JSON.parse(cachedProfile));
      }
      
      // Try to fetch fresh data from server
      if (serverAddress) {
        const response = await fetch(`http://${serverAddress}/api/profile/${match.userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          setMatchProfile(data.profile);
          
          // Cache the profile data
          await AsyncStorage.setItem(`profile_${match.userId}`, JSON.stringify(data.profile));
        }
      }
    } catch (error) {
      console.error('Error fetching match profile:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle view full profile button press
  const handleViewFullProfile = () => {
    navigation.navigate('ViewProfile', {
      userId: match.userId,
      username: match.username,
      forceRefresh: true // Always force refresh from match screen
    });
  };

  return (
    <ScrollView style={styles.matchContainer}>
      <Animated.View 
        style={[styles.matchContent, { opacity: fadeAnim }]}
      >
        <Text style={styles.matchTitle}>It's a Match!</Text>
        <Text style={styles.matchSubtitle}>
          You and {match.username} shook your phones at the same time!
        </Text>
        
        {loading ? (
          <ActivityIndicator size="large" color="#4e9af1" style={styles.loader} />
        ) : (
          <>
            {/* Profile Image Section */}
            <TouchableOpacity 
              style={styles.avatarContainer}
              onPress={handleViewFullProfile}
            >
              {matchProfile && matchProfile.images && matchProfile.images.length > 0 ? (
                <Image 
                  source={{ 
                    uri: matchProfile.images[0].startsWith('data:') 
                      ? matchProfile.images[0] 
                      : `data:image/jpeg;base64,${matchProfile.images[0]}` 
                  }} 
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.largeAvatar}>
                  <Text style={styles.largeAvatarText}>
                    {match.username.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <View style={styles.viewProfileIndicator}>
                <Ionicons name="eye" size={20} color="#fff" />
              </View>
            </TouchableOpacity>
            
            {/* User Info Section */}
            <View style={styles.userInfoContainer}>
              <Text style={styles.userName}>
                {match.username}
                {matchProfile?.age ? `, ${matchProfile.age}` : ''}
              </Text>
              
              <Text style={styles.distanceText}>
                {Math.round(match.distance * 10) / 10} kilometers away
              </Text>
              
              {matchProfile?.description && (
                <Text style={styles.descriptionText}>
                  {matchProfile.description}
                </Text>
              )}
              
              {matchProfile?.passions && matchProfile.passions.length > 0 && (
                <View style={styles.passionsContainer}>
                  {matchProfile.passions.map((passion, index) => (
                    <View key={index} style={styles.passionTag}>
                      <Text style={styles.passionText}>{passion}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </>
        )}
        
        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => navigation.navigate('Chat', { match })}
        >
          <Text style={styles.chatButtonText}>Send a Message</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.viewProfileButton}
          onPress={handleViewFullProfile}
        >
          <Text style={styles.viewProfileText}>View Full Profile</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.laterButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Text style={styles.laterButtonText}>Later</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  matchContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  matchContent: {
    alignItems: 'center',
    padding: 30,
    zIndex: 2,
  },
  matchTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4e9af1',
    marginBottom: 10,
  },
  matchSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  loader: {
    marginVertical: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 20,
    width: 150,
    height: 150,
    borderRadius: 75,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 8,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  largeAvatar: {
    width: '100%',
    height: '100%',
    backgroundColor: '#4e9af1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  largeAvatarText: {
    color: '#fff',
    fontSize: 60,
    fontWeight: 'bold',
  },
  viewProfileIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 8,
    borderTopLeftRadius: 15,
  },
  userInfoContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 30,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  distanceText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  descriptionText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  passionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 10,
  },
  passionTag: {
    backgroundColor: '#f0f8ff',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    margin: 4,
    borderWidth: 1,
    borderColor: '#4e9af1',
  },
  passionText: {
    color: '#4e9af1',
    fontSize: 14,
  },
  chatButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginBottom: 15,
    width: '80%',
    alignItems: 'center',
  },
  chatButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  viewProfileButton: {
    borderWidth: 1,
    borderColor: '#4e9af1',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginBottom: 15,
    width: '80%',
    alignItems: 'center',
  },
  viewProfileText: {
    color: '#4e9af1',
    fontSize: 16,
    fontWeight: '600',
  },
  laterButton: {
    paddingVertical: 10,
  },
  laterButtonText: {
    color: '#999',
    fontSize: 16,
  },
});

export default MatchScreen;