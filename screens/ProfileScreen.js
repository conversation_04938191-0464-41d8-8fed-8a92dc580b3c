// screens/ProfileScreen.js
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Predefined passion options
const PASSION_OPTIONS = [
  'Travel', 'Music', 'Movies', 'Reading', 'Cooking', 'Fitness', 'Photography', 'Art',
  'Dancing', 'Gaming', 'Sports', 'Nature', 'Technology', 'Fashion', 'Food', 'Coffee',
  'Wine', 'Hiking', 'Beach', 'Yoga', 'Meditation', 'Writing', 'Learning', 'Animals',
  'Cars', 'Motorcycles', 'Cycling', 'Running', 'Swimming', 'Skiing', 'Surfing', 'Climbing',
  'Comedy', 'Theater', 'Concerts', 'Festivals', 'Volunteering', 'Gardening', 'DIY',
  'Shopping', 'Nightlife', 'Adventure', 'Culture', 'History', 'Science', 'Politics'
];

const ProfileScreen = ({ navigation, route, user, serverAddress, onUpdateProfile, isInitialSetup = false }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [age, setAge] = useState('');
  const [showAgeModal, setShowAgeModal] = useState(false);
  const [description, setDescription] = useState('');
  const [passions, setPassions] = useState('');
  const [selectedPassions, setSelectedPassions] = useState([]);
  const [images, setImages] = useState(['', '', '', '']);
  const [profileData, setProfileData] = useState(null);
  const [errors, setErrors] = useState({});  // Add state for validation errors
  
  // Refs for input fields (for focusing next input)
  const descriptionRef = useRef(null);
  const passionsRef = useRef(null);

  // Add effect to handle back button and navigation
  useEffect(() => {
    if (isInitialSetup) {
      // Set custom header options to prevent going back
      navigation.setOptions({
        headerTitle: 'Complete Your Profile',
        headerLeft: () => null,  // Remove back button
        gestureEnabled: false,   // Disable swipe back gesture
      });
    }
  }, [navigation, isInitialSetup]);

  // Fetch user profile data when screen loads
  useEffect(() => {
    fetchProfileData();
  }, []);

  // Request permissions for image library
  useEffect(() => {
    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 
            'Sorry, we need camera roll permissions to upload images.');
        }
      }
    })();
  }, []);

  const fetchProfileData = async () => {
    const userId = getValidUserId();
    if (!userId || !serverAddress) return;

    setLoading(true);
    try {
      // Check if profile is cached
      const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);
      if (cachedProfile) {
        const parsedProfile = JSON.parse(cachedProfile);
        setProfileData(parsedProfile);
        setAge(parsedProfile.age ? parsedProfile.age.toString() : '');
        setDescription(parsedProfile.description || '');
        setPassions(parsedProfile.passions ? parsedProfile.passions.join(', ') : '');
        setSelectedPassions(parsedProfile.passions || []);
        setImages(
          parsedProfile.images && parsedProfile.images.length > 0 
            ? [...parsedProfile.images, ...Array(4 - parsedProfile.images.length).fill('')] 
            : ['', '', '', '']
        );
      }

      // Fetch from server
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        if (response.status === 404) {
          // If user not found, it might be a new user without a profile yet
          // This is not necessarily an error
          setLoading(false);
          return;
        }
        throw new Error(errorText || 'Failed to fetch profile');
      }

      const data = await response.json();
      
      setProfileData(data.profile);
      setAge(data.profile.age ? data.profile.age.toString() : '');
      setDescription(data.profile.description || '');
      setPassions(data.profile.passions ? data.profile.passions.join(', ') : '');
      setSelectedPassions(data.profile.passions || []);
      setImages(
        data.profile.images && data.profile.images.length > 0 
          ? [...data.profile.images, ...Array(4 - data.profile.images.length).fill('')] 
          : ['', '', '', '']
      );

      // Cache the profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile));
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Don't show an alert for 404 errors since this could be a new user
      if (!error.message.includes('not found')) {
        Alert.alert('Error', 'Failed to load profile data. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleImagePick = async (startIndex) => {
    try {
      // Count available empty slots starting from the clicked index
      const emptySlots = [];
      for (let i = startIndex; i < 4; i++) {
        if (images[i] === '') {
          emptySlots.push(i);
        }
      }

      // If no empty slots from this position, just replace the clicked slot
      if (emptySlots.length === 0) {
        emptySlots.push(startIndex);
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsMultipleSelection: true,
        selectionLimit: emptySlots.length, // Limit to available slots
        aspect: [1, 1],
        quality: 0.2,
        base64: true,
        exif: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImages = [...images];

        // Fill the available slots with selected images
        result.assets.forEach((asset, assetIndex) => {
          if (assetIndex < emptySlots.length) {
            const slotIndex = emptySlots[assetIndex];
            newImages[slotIndex] = asset.base64;
          }
        });

        setImages(newImages);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };



  const handleRemoveImage = (index) => {
    const newImages = [...images];
    newImages[index] = '';
    setImages(newImages);
  };

  // Handle passion selection with constraints
  const togglePassion = (passion) => {
    setSelectedPassions(prev => {
      if (prev.includes(passion)) {
        // Removing a passion - check minimum constraint
        if (prev.length <= 3) {
          Alert.alert(
            "Minimum Required",
            "Please select at least 3 passions to continue.",
            [{ text: "OK" }]
          );
          return prev; // Don't remove if at minimum
        }
        return prev.filter(p => p !== passion);
      } else {
        // Adding a passion - check maximum constraint
        if (prev.length >= 6) {
          Alert.alert(
            "Maximum Reached",
            "You can select up to 6 passions. Please remove one to add another.",
            [{ text: "OK" }]
          );
          return prev; // Don't add if at maximum
        }
        return [...prev, passion];
      }
    });
  };

  // Handle photo reordering
  const movePhoto = (fromIndex, toIndex) => {
    if (toIndex < 0 || toIndex >= 4 || fromIndex === toIndex) return;

    const newImages = [...images];
    const temp = newImages[fromIndex];
    newImages[fromIndex] = newImages[toIndex];
    newImages[toIndex] = temp;
    setImages(newImages);
  };

  // Debug function to validate user ID before using it
  const getValidUserId = () => {
    if (!user) {
      console.log('User object is null or undefined');
      return null;
    }
    
    if (!user.id) {
      console.log('User ID is missing in the user object', user);
      return null;
    }
    
    // Check if the ID looks like a valid MongoDB ObjectId
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(user.id);
    console.log(`User ID: ${user.id}, valid ObjectId format: ${isValidObjectId}`);
    
    return user.id;
  };

  // Add this function to validate the profile
  const validateProfile = () => {
    const newErrors = {};

    if (!description.trim()) {
      newErrors.description = 'Please add a description about yourself';
    }

    if (!age) {
        newErrors.age = 'Age is required';
      } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum) || ageNum < 18 || ageNum > 120) {
          newErrors.age = 'Please enter a valid age between 18 and 120';
        }
      }

    // Check passion selection constraints
    if (selectedPassions.length < 3) {
      newErrors.passions = `Please select at least 3 passions (${selectedPassions.length}/3 selected)`;
    } else if (selectedPassions.length > 6) {
      newErrors.passions = `Please select no more than 6 passions (${selectedPassions.length}/6 selected)`;
    }

    // Check for minimum of 3 pictures
    if (isInitialSetup) {
      const photoCount = images.filter(img => img !== '').length;
      if (photoCount < 3) {
        newErrors.photos = `Please add at least 3 photos (${photoCount}/3 uploaded)`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    
    const userId = getValidUserId();
    if (!userId || !serverAddress) {
      Alert.alert('Error', 'Invalid user ID or server address. Please try logging in again.');
      return;
    }

    // Validate profile data
    if (!validateProfile()) {
      // If there are validation errors and this is initial setup, show an alert
      if (isInitialSetup) {
        Alert.alert(
          'Incomplete Profile',
          'Please complete your profile before continuing. A description about yourself is required.',
          [{ text: 'OK' }]
        );
      }
      return;
    }

    setSaving(true);
    try {
      // Filter out empty images and ensure proper format
      const filteredImages = images
        .filter(img => img !== '')
        .map(img => {
          // If it's already a data URI, extract just the base64 part
          if (img.startsWith('data:image')) {
            return img.split(',')[1];
          }
          // Return as is if it's just the base64 string
          return img;
        });
      
      // Check if images are too large
      const totalImagesSize = JSON.stringify(filteredImages).length;
      if (totalImagesSize > 5000000) { // ~5MB limit
        Alert.alert(
          'Images Too Large', 
          'Your images are too large. Please try using fewer or smaller images.',
          [{ text: 'OK' }]
        );
        setSaving(false);
        return;
      }
      
      const profileData = {
        age: age ? parseInt(age) : null,
        description,
        passions: selectedPassions,
        images: filteredImages,
      };

      console.log(`Saving profile for user ID: ${userId}`);
      
      const response = await fetch(`http://${serverAddress}/api/profile/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`Error response from server: ${errorText}`);
        throw new Error(errorText || 'Failed to update profile');
      }
      
      const data = await response.json().catch(e => {
        console.log('Error parsing JSON response:', e);
        return { profile: profileData };
      });

      // Cache the updated profile data
      await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(data.profile || profileData));
      
      // Update app state if callback provided
      if (onUpdateProfile) {
        onUpdateProfile(data.profile || profileData);
      }

      if (isInitialSetup) {
        // Show alert and then navigate to Home screen
        Alert.alert(
          'Profile Completed',
          'Your profile has been set up. You can now use the app!',
          [
            { 
              text: 'Continue', 
              onPress: () => {
                // Explicitly navigate to Home screen after profile setup
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Home' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Success', 'Your profile has been updated successfully.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', `Failed to save profile: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4e9af1" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : null}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView 
          style={styles.container}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {isInitialSetup && (
            <View style={styles.header}>
              <Text style={styles.requiredNote}>
                * Description, age, and at least 3 photos are required
              </Text>
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Photos</Text>
            <Text style={styles.sectionSubtitle}>
              {isInitialSetup
                ? <Text>Add at least 3 photos <Text style={styles.requiredStar}>*</Text></Text>
                : 'Add up to 4 photos'}
            </Text>
            {errors.photos && <Text style={styles.errorText}>{errors.photos}</Text>}


            <View style={styles.imagesContainer}>
              {images.map((image, index) => (
                <View key={index} style={styles.imageBox}>
                  {image ? (
                    <View style={styles.imageWrapper}>
                      <Image
                        source={{ uri: image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}` }}
                        style={styles.image}
                      />

                      {/* Reorder buttons */}
                      <View style={styles.reorderButtons}>
                        {index > 0 && (
                          <TouchableOpacity
                            style={styles.reorderButton}
                            onPress={() => movePhoto(index, index - 1)}
                          >
                            <Ionicons name="chevron-back" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                        {index < 3 && (
                          <TouchableOpacity
                            style={styles.reorderButton}
                            onPress={() => movePhoto(index, index + 1)}
                          >
                            <Ionicons name="chevron-forward" size={16} color="#fff" />
                          </TouchableOpacity>
                        )}
                      </View>

                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <Ionicons name="close-circle" size={24} color="#ff3b30" />
                      </TouchableOpacity>

                      {/* Photo number indicator */}
                      <View style={styles.photoNumber}>
                        <Text style={styles.photoNumberText}>{index + 1}</Text>
                      </View>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.addImageButton}
                      onPress={() => handleImagePick(index)}
                    >
                      <Ionicons name="add" size={40} color="#4e9af1" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About You</Text>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Age {<Text style={styles.requiredStar}>*</Text>}
              </Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.age && styles.inputError]}
                onPress={() => setShowAgeModal(true)}
              >
                <Text style={[styles.dropdownText, !age && styles.placeholderText]}>
                  {age || 'Select your age'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </TouchableOpacity>
              {errors.age && <Text style={styles.errorText}>{errors.age}</Text>}
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Description <Text style={styles.requiredStar}>*</Text>
              </Text>
              <TextInput
                ref={descriptionRef}
                style={[styles.input, styles.textArea, errors.description && styles.inputError]}
                placeholder="Share a little about yourself..."
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                maxLength={500}
                returnKeyType="next"
                onSubmitEditing={() => passionsRef.current && passionsRef.current.focus()}
                blurOnSubmit={false}
                autoCorrect={false}
                autoCompleteType="off"
                textContentType="none"
                spellCheck={false}
              />
              <Text style={styles.charCount}>{description.length}/500</Text>
              {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            </View>
            
            <View style={styles.formGroup}>
              <Text style={styles.label}>
                Passions <Text style={styles.requiredStar}>*</Text>
              </Text>
              <Text style={styles.helperText}>
                Select 3-6 interests that represent you.
              </Text>
              {errors.passions && <Text style={styles.errorText}>{errors.passions}</Text>}

              <View style={styles.passionContainer}>
                {PASSION_OPTIONS.map((passion) => (
                  <TouchableOpacity
                    key={passion}
                    style={[
                      styles.passionTag,
                      selectedPassions.includes(passion) && styles.selectedPassionTag,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && styles.disabledPassionTag
                    ]}
                    onPress={() => togglePassion(passion)}
                    disabled={selectedPassions.length >= 6 && !selectedPassions.includes(passion)}
                  >
                    <Text style={[
                      styles.passionTagText,
                      selectedPassions.includes(passion) && styles.selectedPassionTagText,
                      selectedPassions.length >= 6 && !selectedPassions.includes(passion) && styles.disabledPassionTagText
                    ]}>
                      {passion}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.passionStatus}>
                <Text style={[
                  styles.selectedCount,
                  selectedPassions.length < 3 && styles.warningText,
                  selectedPassions.length >= 3 && selectedPassions.length <= 6 && styles.successText
                ]}>
                  {selectedPassions.length}/6 passions selected
                  {selectedPassions.length < 3 && ` (need ${3 - selectedPassions.length} more)`}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveProfile}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.saveButtonText}>
                  {isInitialSetup ? 'Complete Profile' : 'Save Profile'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
          
          {/* Add padding at the bottom for iOS keyboard */}
          {Platform.OS === 'ios' && <View style={styles.keyboardPadding} />}
        </ScrollView>
      </TouchableWithoutFeedback>

      {/* Age Selection Modal */}
      <Modal
        visible={showAgeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAgeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Your Age</Text>
              <TouchableOpacity
                onPress={() => setShowAgeModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.ageList} showsVerticalScrollIndicator={false}>
              {Array.from({ length: 103 }, (_, i) => i + 18).map((ageOption) => (
                <TouchableOpacity
                  key={ageOption}
                  style={[
                    styles.ageOption,
                    age === ageOption.toString() && styles.selectedAgeOption
                  ]}
                  onPress={() => {
                    setAge(ageOption.toString());
                    setShowAgeModal(false);
                  }}
                >
                  <Text style={[
                    styles.ageOptionText,
                    age === ageOption.toString() && styles.selectedAgeOptionText
                  ]}>
                    {ageOption}
                  </Text>
                  {age === ageOption.toString() && (
                    <Ionicons name="checkmark" size={20} color="#4e9af1" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  scrollContentContainer: {
    paddingBottom: Platform.OS === 'ios' ? 100 : 30, // Extra padding for iOS
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  requiredNote: {
    fontSize: 14,
    color: '#ff3b30',
    marginTop: 5,
  },
  requiredStar: {
    color: '#ff3b30',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 15,
    padding: 20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  imageBox: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
  addImageButton: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 30, // Increased space between form groups
    zIndex: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 8,
    fontSize: 14,
  },
  inputError: {
    borderColor: '#ff3b30',
    borderWidth: 1,
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 5,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    alignSelf: 'flex-end',
    fontSize: 12,
    color: '#999',
    marginTop: 5,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  buttonContainer: {
    padding: 20,
    marginBottom: 30,
  },
  saveButton: {
    backgroundColor: '#4e9af1',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  keyboardPadding: {
    height: 60, // Extra padding for iOS keyboard
  },
  // Age dropdown styles
  dropdownInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Platform.OS === 'ios' ? 12 : 10,
  },
  dropdownText: {
    fontSize: 14,
    color: '#2c384a',
    fontWeight: '500',
  },
  placeholderText: {
    color: '#8a9cb0',
    fontWeight: '400',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalCloseButton: {
    padding: 5,
  },
  ageList: {
    maxHeight: 300,
  },
  ageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedAgeOption: {
    backgroundColor: '#f0f8ff',
  },
  ageOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedAgeOptionText: {
    color: '#4e9af1',
    fontWeight: '600',
  },
  // Passion tags styles
  passionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  passionTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    margin: 2,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedPassionTag: {
    backgroundColor: '#4e9af1',
    borderColor: '#4e9af1',
  },
  passionTagText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  selectedPassionTagText: {
    color: '#fff',
    fontWeight: '600',
  },
  disabledPassionTag: {
    backgroundColor: '#f8f8f8',
    borderColor: '#e8e8e8',
    opacity: 0.5,
  },
  disabledPassionTagText: {
    color: '#ccc',
  },
  passionStatus: {
    marginTop: 10,
    alignItems: 'center',
  },
  selectedCount: {
    fontSize: 12,
    color: '#4e9af1',
    marginTop: 8,
    fontWeight: '500',
  },
  warningText: {
    color: '#ff9500',
  },
  successText: {
    color: '#34c759',
  },

  // Photo reordering styles
  reorderButtons: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  reorderButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoNumber: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoNumberText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ProfileScreen;