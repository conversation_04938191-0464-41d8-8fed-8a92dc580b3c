// screens/RegisterScreen.js - Modern design with fixed keyboard issues
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  Keyboard,
  Dimensions,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const RegisterScreen = ({ navigation, serverAddress: propServerAddress }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [serverAddress, setServerAddress] = useState(propServerAddress || '**************:3000');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [screenHeight, setScreenHeight] = useState(Dimensions.get('window').height);
  
  // Animation values
  const logoPosition = useRef(new Animated.Value(0)).current;
  const formOpacity = useRef(new Animated.Value(1)).current;
  
  // Reference for text inputs
  const passwordInputRef = useRef(null);
  const confirmPasswordInputRef = useRef(null);

  // Handle keyboard appearance and animations
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardVisible(true);
        // Calculate how much we need to move the form up to avoid keyboard
        const keyboardHeight = event.endCoordinates.height;
        
        // On iOS we need to consider the keyboard takes up more space
        const formHeight = 350; // Estimated form height (larger for register)
        const contentHeight = 500; // Estimated total content height
        const availableHeight = screenHeight - keyboardHeight;
        
        // Calculate how much we need to move to ensure fields are visible
        // Using your adjustment of +200 instead of +100
        const moveUpValue = Platform.OS === 'ios' 
          ? -Math.min(contentHeight - availableHeight + 200, 250) 
          : -Math.min(keyboardHeight * 0.5, 150);
        
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: moveUpValue,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 0.97,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );
    
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );

    // Screen dimensions listener for orientation changes
    const dimensionListener = Dimensions.addEventListener(
      'change',
      ({ window }) => {
        setScreenHeight(window.height);
      }
    );

    // Cleanup
    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
      dimensionListener.remove();
    };
  }, []);

  const handleRegister = async () => {
    Keyboard.dismiss();
    
    // Basic validation
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      // Save server address for login screen to use
      await AsyncStorage.setItem('serverAddress', serverAddress);

      // Connect to server
      const response = await fetch(`http://${serverAddress}/api/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Registration successful
      Alert.alert(
        'Registration Successful',
        'Your account has been created. You can now log in.',
        [{ text: 'OK', onPress: () => navigation.replace('Login') }]
      );
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert(
        'Registration Failed',
        'Could not create account. Please try a different username.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Fixed function to handle navigation back to Login screen
  const goToLogin = () => {
    // Use replace instead of navigate to avoid stacking screens
    navigation.replace('Login');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#4e9af1" />
      
      <Animated.View 
        style={[
          styles.container,
          { transform: [{ translateY: logoPosition }] }
        ]}
      >
        {/* Background gradient */}
        <View style={styles.backgroundGradient} />
        
        {/* Floating shapes - purely decorative */}
        <View style={[styles.floatingShape, styles.floatingShape1]} />
        <View style={[styles.floatingShape, styles.floatingShape2]} />
        <View style={[styles.floatingShape, styles.floatingShape3]} />
        
        {/* Logo section */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
            fadeDuration={0}
          />
          <Text style={styles.appName}>Shake & Match</Text>
          <Text style={styles.tagline}>Create a new account</Text>
        </View>

        {/* Form section with animation */}
        <Animated.View 
          style={[
            styles.formContainer,
            { 
              opacity: formOpacity,
              transform: [
                { 
                  scale: formOpacity.interpolate({
                    inputRange: [0.97, 1],
                    outputRange: [0.98, 1]
                  }) 
                }
              ] 
            }
          ]}
        >
          {/* Username input */}
          <View style={styles.fieldWrapper}>
            <View style={styles.inputContainer}>
              <Ionicons name="person-outline" size={24} color="#4e9af1" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Choose a Username"
                placeholderTextColor="#8a9cb0"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="next"
                onSubmitEditing={() => passwordInputRef.current?.focus()}
                textContentType="username"
                autoComplete="username-new"
              />
            </View>
          </View>
          
          {/* Password input */}
          <View style={styles.fieldWrapper}>
            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={24} color="#4e9af1" style={styles.inputIcon} />
              <TextInput
                ref={passwordInputRef}
                style={[styles.input, styles.passwordInput]}
                placeholder="Password"
                placeholderTextColor="#8a9cb0"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="next"
                onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                textContentType="newPassword"
                autoComplete="password-new"
                importantForAutofill="yes"
                passwordRules="minlength: 6;"
              />
            </View>
          </View>
          
          {/* Confirm Password input */}
          <View style={styles.fieldWrapper}>
            <View style={styles.inputContainer}>
              <Ionicons name="shield-checkmark-outline" size={24} color="#4e9af1" style={styles.inputIcon} />
              <TextInput
                ref={confirmPasswordInputRef}
                style={[styles.input, styles.passwordInput]}
                placeholder="Confirm Password"
                placeholderTextColor="#8a9cb0"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="done"
                onSubmitEditing={handleRegister}
                textContentType="newPassword"
                autoComplete="password-new"
                importantForAutofill="yes"
                passwordRules="minlength: 6;"
              />
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.registerButton}
            onPress={handleRegister}
            disabled={isLoading}
            activeOpacity={0.85}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Text style={styles.registerButtonText}>Create Account</Text>
                <Ionicons name="arrow-forward" size={20} color="#fff" style={styles.buttonIcon} />
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.loginLinkButton}
            onPress={goToLogin}
            activeOpacity={0.7}
          >
            <Text style={styles.loginLinkText}>
              Already have an account? <Text style={styles.loginLinkTextBold}>Sign In</Text>
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#4e9af1',
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingTop: Platform.OS === 'ios' ? 20 : 0,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#4e9af1',
    zIndex: -2,
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 80,
    zIndex: -1,
  },
  floatingShape1: {
    width: 200,
    height: 200,
    top: -60,
    right: -60,
    transform: [{ rotate: '30deg' }],
  },
  floatingShape2: {
    width: 180,
    height: 180,
    top: '65%',
    left: -90,
    transform: [{ rotate: '15deg' }],
  },
  floatingShape3: {
    width: 120,
    height: 120,
    bottom: 100,
    right: -20,
    transform: [{ rotate: '45deg' }],
    opacity: 0.6,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 35,
    width: '100%',
  },
  logo: {
    width: 110,
    height: 110,
  },
  appName: {
    fontSize: 26,
    fontWeight: 'bold',
    marginTop: 12,
    color: '#fff',
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  formContainer: {
    width: '100%',
    backgroundColor: 'white',
    padding: 25,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
  },
  fieldWrapper: {
    marginVertical: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#e8eef4',
    borderRadius: 14,
    paddingHorizontal: 18,
    height: 60,
    backgroundColor: '#f8fafc',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#2c384a',
    fontWeight: '500',
    paddingVertical: Platform.OS === 'ios' ? 18 : 16,
    textAlignVertical: 'center',
  },
  passwordInput: {
    paddingVertical: Platform.OS === 'ios' ? 18 : 16,
    textAlignVertical: 'center',
    ...(Platform.OS === 'android' && {
      includeFontPadding: false,
      textAlignVertical: 'center',
    }),
  },
  registerButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 16,
    borderRadius: 14,
    alignItems: 'center',
    marginTop: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#4e9af1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.35,
    shadowRadius: 12,
    elevation: 6,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  buttonIcon: {
    marginLeft: 8,
  },
  loginLinkButton: {
    alignItems: 'center',
    marginTop: 20,
    padding: 12,
  },
  loginLinkText: {
    color: '#8a9cb0',
    fontSize: 15,
  },
  loginLinkTextBold: {
    color: '#4e9af1',
    fontWeight: '600',
  }
});

export default RegisterScreen;