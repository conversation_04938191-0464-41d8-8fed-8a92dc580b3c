// screens/SettingsScreen.js
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  ScrollView,
  FlatList,
  Platform,
  Linking
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';

const SettingsScreen = ({ 
  navigation, 
  maxDistance, 
  minAge = 18,
  maxAge = 100,
  blockedUsers = [],
  onUpdateMaxDistance,
  onUpdateAgeRange,
  onUnblockUser,
  onLogout 
}) => {
 
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [distance, setDistance] = useState(maxDistance);
  const [minimumAge, setMinimumAge] = useState(minAge);
  const [maximumAge, setMaximumAge] = useState(maxAge);
  const [blockedUsernames, setBlockedUsernames] = useState({});
  const [locationPermission, setLocationPermission] = useState(null);
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);

  // Check location permission status on mount
  useEffect(() => {
    checkLocationPermission();
  }, []);

  // Check location permission
  const checkLocationPermission = async () => {
    setIsCheckingLocation(true);
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
    } catch (error) {
      console.error('Error checking location permission:', error);
    } finally {
      setIsCheckingLocation(false);
    }
  };

  // Request location permission
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status !== 'granted') {
        // If permission denied, show alert with option to open settings
        Alert.alert(
          "Location Permission Required",
          "Shake & Match needs location permission to find matches near you. Please enable location in your device settings.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Open Settings", 
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              } 
            }
          ]
        );
      } else {
        Alert.alert(
          "Location Enabled",
          "Location permission granted! You can now match with nearby users."
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  // Load blocked usernames
  useEffect(() => {
    const loadBlockedUsernames = async () => {
      try {
        const usernamesObj = {};
        
        for (const userId of blockedUsers) {
          // Try to get username from storage
          const key = `username_${userId}`;
          const storedUsername = await AsyncStorage.getItem(key);
          
          if (storedUsername) {
            usernamesObj[userId] = storedUsername;
          } else {
            usernamesObj[userId] = 'Unknown User';
          }
        }
        
        setBlockedUsernames(usernamesObj);
      } catch (error) {
        console.error('Error loading blocked usernames:', error);
      }
    };
    
    if (blockedUsers.length > 0) {
      loadBlockedUsernames();
    }
  }, [blockedUsers]);

  // Handle unblock user button press
  const handleUnblockUser = (userId) => {
    Alert.alert(
      'Unblock User',
      `Are you sure you want to unblock ${blockedUsernames[userId] || 'this user'}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Unblock', 
          style: 'destructive',
          onPress: () => {
            if (onUnblockUser) {
              onUnblockUser(userId);
            }
          }
        }
      ]
    );
  };

  // Handle distance change
  const handleDistanceChange = (value) => {
    setDistance(value);
    onUpdateMaxDistance(value);
  };

  // Handle min age change
  const handleMinAgeChange = (value) => {
    // Set the value directly from the slider
    setMinimumAge(value);
    
    // If the minimum age is now greater than or equal to maximum age, 
    // also increase the maximum age
    if (value >= maximumAge) {
      const newMaxAge = value + 1;
      setMaximumAge(newMaxAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, newMaxAge);
      }
    } else {
      // Otherwise just update the minimum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, maximumAge);
      }
    }
  };

  // Handle max age change
  const handleMaxAgeChange = (value) => {
    // Set the value directly from the slider
    setMaximumAge(value);
    
    // If the maximum age is now less than or equal to minimum age, 
    // also decrease the minimum age
    if (value <= minimumAge) {
      const newMinAge = value - 1;
      setMinimumAge(newMinAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(newMinAge, value);
      }
    } else {
      // Otherwise just update the maximum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(minimumAge, value);
      }
    }
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: onLogout }
      ]
    );
  };

  // Render blocked user item
  const renderBlockedUser = ({ item }) => {
    const userId = item;
    const username = blockedUsernames[userId] || 'Unknown User';
    
    return (
      <View style={styles.blockedUserItem}>
        <View style={styles.blockedUserInfo}>
          <View style={styles.blockedUserAvatar}>
            <Text style={styles.blockedUserAvatarText}>
              {username.charAt(0).toUpperCase()}
            </Text>
          </View>
          <Text style={styles.blockedUserName}>{username}</Text>
        </View>
        <TouchableOpacity
          style={styles.unblockButton}
          onPress={() => handleUnblockUser(userId)}
        >
          <Text style={styles.unblockButtonText}>Unblock</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Get location status text and color
  const getLocationStatusInfo = () => {
    if (locationPermission === 'granted') {
      return {
        text: 'Location services enabled',
        color: '#4e9af1',
        icon: 'checkmark-circle'
      };
    } else if (locationPermission === 'denied') {
      return {
        text: 'Location services disabled',
        color: '#ff3b30',
        icon: 'alert-circle'
      };
    } else {
      return {
        text: 'Location permission not determined',
        color: '#ff9500',
        icon: 'help-circle'
      };
    }
  };

  const locationInfo = getLocationStatusInfo();

  return (
    <ScrollView style={styles.container}>
      {/* Location Services Section - NEW */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Location Services</Text>
        
        <View style={styles.locationStatusContainer}>
          <Ionicons name={locationInfo.icon} size={24} color={locationInfo.color} style={styles.locationIcon} />
          <Text style={[styles.locationStatusText, { color: locationInfo.color }]}>
            {locationInfo.text}
          </Text>
        </View>
        
        <Text style={styles.locationDescription}>
          Shake & Match requires location services to find nearby matches. Without location access, the app cannot function properly.
        </Text>
        
        <TouchableOpacity
          style={[
            styles.locationButton,
            { backgroundColor: locationPermission === 'granted' ? '#e0e0e0' : '#4e9af1' }
          ]}
          onPress={requestLocationPermission}
          disabled={isCheckingLocation || locationPermission === 'granted'}
        >
          {isCheckingLocation ? (
            <Text style={styles.locationButtonText}>Checking...</Text>
          ) : (
            <Text style={styles.locationButtonText}>
              {locationPermission === 'granted' ? 'Location Enabled' : 'Enable Location'}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Match Settings</Text>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Maximum Match Distance</Text>
          <Text style={styles.settingValue}>{Math.round(distance)} km</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={1}
          maximumValue={50}
          step={1}
          value={distance}
          onValueChange={handleDistanceChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>1 km</Text>
          <Text style={styles.sliderLabel}>50 km</Text>
        </View>
        
        <View style={styles.divider} />
        
        {/* Minimum Age Setting */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Minimum Age</Text>
          <Text style={styles.settingValue}>{minimumAge} years</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={18}
          maximumValue={75}
          step={1}
          value={minimumAge}
          onValueChange={handleMinAgeChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>18 yrs</Text>
          <Text style={styles.sliderLabel}>75 yrs</Text>
        </View>
        
        {/* Maximum Age Setting */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Maximum Age</Text>
          <Text style={styles.settingValue}>{maximumAge} years</Text>
        </View>
        
        <Slider
          style={styles.slider}
          minimumValue={19}
          maximumValue={99}
          step={1}
          value={maximumAge}
          onValueChange={handleMaxAgeChange}
          minimumTrackTintColor="#4e9af1"
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor="#4e9af1"
        />
        
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>19 yrs</Text>
          <Text style={styles.sliderLabel}>99 yrs</Text>
        </View>
        
        <Text style={styles.settingDescription}>
          You will only be matched with users who are between {minimumAge} and {maximumAge} years old.
        </Text>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>
        
        <View style={styles.settingToggle}>
          <Text style={styles.settingLabel}>Enable Notifications</Text>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#d3d3d3', true: '#4e9af1' }}
            thumbColor="#fff"
          />
        </View>
      </View>
      
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Blocked Users</Text>
        
        {blockedUsers.length === 0 ? (
          <Text style={styles.noBlockedText}>You haven't blocked any users.</Text>
        ) : (
          <FlatList
            data={blockedUsers}
            keyExtractor={(item) => item}
            renderItem={renderBlockedUser}
            scrollEnabled={false}
          />
        )}
      </View>
      
      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={24} color="#fff" />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 15,
    marginTop: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  // Location services styles (NEW)
  locationStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
  },
  locationIcon: {
    marginRight: 10,
  },
  locationStatusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  locationDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  locationButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  locationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // Original styles
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#444',
  },
  settingValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4e9af1',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
    fontStyle: 'italic',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: -10,
  },
  sliderLabel: {
    fontSize: 14,
    color: '#999',
  },
  settingToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 15,
  },
  updateButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },
  updateButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  serverNote: {
    fontSize: 14,
    color: '#888',
    marginTop: 10,
    fontStyle: 'italic',
  },
  noBlockedText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    paddingVertical: 15,
  },
  blockedUserItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  blockedUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  blockedUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f85149',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  blockedUserAvatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  blockedUserName: {
    fontSize: 16,
    color: '#333',
  },
  unblockButton: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  unblockButtonText: {
    color: '#f85149',
    fontSize: 14,
    fontWeight: '500',
  },
  logoutButton: {
    backgroundColor: '#f85149',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    borderRadius: 10,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default SettingsScreen;