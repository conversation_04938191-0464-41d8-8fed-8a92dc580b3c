# Social Login Setup Guide

This guide will help you configure Google, Apple, and Facebook login for your Shake & Match app.

## Overview

The app now supports social login instead of traditional account creation. Users can sign in with:
- Google (iOS & Android) - Using Expo AuthSession
- Apple (iOS only, iOS 13+) - Using Expo Apple Authentication
- Facebook (iOS & Android) - Using Expo AuthSession

**Note:** This implementation uses Expo's AuthSession which works with Expo Go and doesn't require native module compilation.

## Configuration Steps

### 1. Google Sign-In Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (or Google Sign-In API)

#### Step 2: Create OAuth 2.0 Credentials
1. Go to "Credentials" in the Google Cloud Console
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Create three client IDs:

**Web Application:**
- Application type: Web application
- Name: "Shake & Match Web"
- Authorized redirect URIs: Add `https://auth.expo.io/@your-username/shake-and-match`
- Copy the Client ID (this is your `webClientId`)

**Note:** With Expo AuthSession, you only need the Web Client ID. The iOS and Android client IDs are not required.

#### Step 3: Update Configuration
1. Open `config/socialAuth.js`
2. Replace `YOUR_GOOGLE_WEB_CLIENT_ID` with your actual Web Client ID

### 2. Facebook Login Setup

#### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or select existing one
3. Add "Facebook Login" product

#### Step 2: Configure Facebook Login
1. In Facebook Login settings, add these OAuth Redirect URIs:
   - Add `https://auth.expo.io/@your-username/shake-and-match`

#### Step 3: Update Configuration
1. Open `config/socialAuth.js`
2. Replace `YOUR_FACEBOOK_APP_ID` with your actual Facebook App ID
3. Replace `YOUR_FACEBOOK_APP_SECRET` with your actual Facebook App Secret

**Note:** With Expo AuthSession, you don't need to configure native Android files.

### 3. Apple Sign-In Setup

#### Step 1: Enable Apple Sign-In
1. Go to [Apple Developer Account](https://developer.apple.com/account/)
2. Go to "Certificates, Identifiers & Profiles"
3. Select your App ID
4. Enable "Sign In with Apple"

#### Step 2: No Code Changes Needed
Apple Sign-In is automatically configured using your bundle identifier.

## Testing

### Development Testing
1. Make sure you've updated all configuration files
2. Run `expo start` to start the development server
3. Test on physical devices (social login doesn't work well in simulators)

### Production Testing
1. Build your app for production
2. Test all three social login methods
3. Verify user data is correctly stored in your database

## Troubleshooting

### Google Sign-In Issues
- Make sure your SHA-1 certificate fingerprint is correct for Android
- Verify bundle identifier matches exactly for iOS
- Check that Google+ API is enabled

### Facebook Login Issues
- Verify Facebook App ID is correct
- Make sure OAuth redirect URIs are properly configured
- Check that Facebook Login product is added to your app

### Apple Sign-In Issues
- Only works on iOS 13+ devices
- Make sure "Sign In with Apple" is enabled for your App ID
- Test on physical iOS devices, not simulators

## Files Modified

The following files have been modified to support social login:

1. `screens/LoginScreen.js` - Added social login buttons and handlers
2. `services/SocialAuthService.js` - Social authentication service
3. `Server/server.js` - Added `/api/social-login` endpoint
4. `Server/models/User.js` - Added social auth fields to user model
5. `App.js` - Removed registration screen from navigation
6. `app.json` - Added social login plugin configurations
7. `config/socialAuth.js` - Configuration file for social auth credentials

## Next Steps

1. Update the configuration files with your actual credentials
2. Test the social login functionality
3. Deploy your updated server with the new social login endpoint
4. Build and test your mobile app

Remember to keep your social login credentials secure and never commit them to version control in production!
