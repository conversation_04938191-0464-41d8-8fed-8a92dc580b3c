// index.js - Updated for navigation test
// Import polyfills first
import './timer-polyfills';

// Standard imports
import { AppRegistry } from 'react-native';

// Change this line to test different app versions
import App from './App.js';
// import App from './UltraMinimalApp'; 

console.log('Starting app with navigation');

// Register with the name "main"
AppRegistry.registerComponent('main', () => App);

console.log('App registered successfully');