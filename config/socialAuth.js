// config/socialAuth.js
// Social Authentication Configuration
// Replace these placeholder values with your actual app credentials

export const SOCIAL_AUTH_CONFIG = {
  google: {
    webClientId: 'YOUR_GOOGLE_WEB_CLIENT_ID.apps.googleusercontent.com',
  },
  facebook: {
    appId: 'YOUR_FACEBOOK_APP_ID',
    appSecret: 'YOUR_FACEBOOK_APP_SECRET', // Needed for token exchange
    appName: 'Shake & Match',
  },
  apple: {
    // Apple Sign-In doesn't require additional configuration
    // It uses your app's bundle identifier
  }
};

// Instructions for setting up social authentication:
/*
GOOGLE SIGN-IN SETUP:
1. Go to https://console.developers.google.com/
2. Create a new project or select existing one
3. Enable Google+ API
4. Create credentials (OAuth 2.0 client IDs) for:
   - Web application (for web client ID)
   - iOS application (using your bundle identifier)
   - Android application (using your package name and SHA-1 certificate fingerprint)
5. Replace the placeholder values above with your actual client IDs

FACEBOOK LOGIN SETUP:
1. Go to https://developers.facebook.com/
2. Create a new app or select existing one
3. Add Facebook Login product
4. Configure OAuth redirect URIs
5. Replace YOUR_FACEBOOK_APP_ID with your actual app ID
6. Add your bundle identifier and package name to the app settings

APPLE SIGN-IN SETUP:
1. Go to https://developer.apple.com/account/
2. Enable Sign In with Apple for your app identifier
3. No additional configuration needed in code
4. Apple Sign-In is automatically available on iOS 13+ devices

ANDROID FACEBOOK SETUP:
You'll also need to add your Facebook App ID to android/app/src/main/res/values/strings.xml:
<string name="facebook_app_id">YOUR_FACEBOOK_APP_ID</string>

And update android/app/src/main/AndroidManifest.xml with Facebook configuration.
*/
