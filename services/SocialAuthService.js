// services/SocialAuthService.js
import * as AppleAuthentication from 'expo-apple-authentication';
import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import { Platform, Alert } from 'react-native';
import { SOCIAL_AUTH_CONFIG } from '../config/socialAuth';

class SocialAuthService {
  constructor() {
    this.initializeProviders();
  }

  async initializeProviders() {
    // No initialization needed for Expo AuthSession
    console.log('Social auth providers initialized');
  }

  // Google Sign-In using Expo AuthSession
  async signInWithGoogle() {
    try {
      const redirectUri = AuthSession.makeRedirectUri({
        useProxy: true,
      });

      const responseType = AuthSession.ResponseType.Code;
      const extraParams = {};
      const scopes = ['openid', 'profile', 'email'];

      const authRequestConfig = {
        responseType,
        clientId: SOCIAL_AUTH_CONFIG.google.webClientId,
        redirectUri,
        scopes,
        extraParams,
      };

      const authRequest = new AuthSession.AuthRequest(authRequestConfig);
      const authorizeResult = await authRequest.promptAsync({
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      });

      if (authorizeResult.type !== 'success') {
        throw new Error('Google sign-in was canceled or failed');
      }

      // Exchange code for tokens
      const tokenResult = await AuthSession.exchangeCodeAsync(
        {
          clientId: SOCIAL_AUTH_CONFIG.google.webClientId,
          code: authorizeResult.params.code,
          redirectUri,
          extraParams: {},
        },
        {
          tokenEndpoint: 'https://oauth2.googleapis.com/token',
        }
      );

      // Get user info
      const userInfoResponse = await fetch(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokenResult.accessToken}`
      );
      const userInfo = await userInfoResponse.json();

      return {
        provider: 'google',
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        photo: userInfo.picture,
        token: tokenResult.accessToken,
      };
    } catch (error) {
      console.error('Google Sign-In error:', error);
      throw new Error('Google sign-in failed');
    }
  }

  // Apple Sign-In
  async signInWithApple() {
    try {
      // Check if Apple Authentication is available
      const isAvailable = await AppleAuthentication.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('Apple Sign-In is not available on this device');
      }

      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      return {
        provider: 'apple',
        id: credential.user,
        email: credential.email,
        name: credential.fullName ? 
          `${credential.fullName.givenName || ''} ${credential.fullName.familyName || ''}`.trim() : 
          null,
        token: credential.identityToken,
      };
    } catch (error) {
      console.error('Apple Sign-In error:', error);
      if (error.code === 'ERR_CANCELED') {
        throw new Error('Apple sign-in was canceled');
      }
      throw new Error('Apple sign-in failed');
    }
  }

  // Facebook Sign-In using Expo AuthSession
  async signInWithFacebook() {
    try {
      const redirectUri = AuthSession.makeRedirectUri({
        useProxy: true,
      });

      const responseType = AuthSession.ResponseType.Code;
      const scopes = ['public_profile', 'email'];

      const authRequestConfig = {
        responseType,
        clientId: SOCIAL_AUTH_CONFIG.facebook.appId,
        redirectUri,
        scopes,
        extraParams: {},
      };

      const authRequest = new AuthSession.AuthRequest(authRequestConfig);
      const authorizeResult = await authRequest.promptAsync({
        authorizationEndpoint: 'https://www.facebook.com/v18.0/dialog/oauth',
      });

      if (authorizeResult.type !== 'success') {
        throw new Error('Facebook sign-in was canceled or failed');
      }

      // Exchange code for access token
      const tokenResponse = await fetch(
        `https://graph.facebook.com/v18.0/oauth/access_token?` +
        `client_id=${SOCIAL_AUTH_CONFIG.facebook.appId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `client_secret=${SOCIAL_AUTH_CONFIG.facebook.appSecret}&` +
        `code=${authorizeResult.params.code}`
      );

      const tokenData = await tokenResponse.json();

      if (!tokenData.access_token) {
        throw new Error('Failed to get Facebook access token');
      }

      // Get user info
      const userInfoResponse = await fetch(
        `https://graph.facebook.com/me?fields=id,name,email,picture.type(large)&access_token=${tokenData.access_token}`
      );
      const userInfo = await userInfoResponse.json();

      return {
        provider: 'facebook',
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        photo: userInfo.picture?.data?.url,
        token: tokenData.access_token,
      };
    } catch (error) {
      console.error('Facebook Sign-In error:', error);
      throw new Error('Facebook sign-in failed');
    }
  }

  // Sign out from all providers
  async signOut() {
    try {
      // For Expo AuthSession, we don't need to explicitly sign out
      // The tokens are managed by the app, not the native SDKs
      console.log('Signed out from social providers');

      // Note: Apple doesn't provide a sign-out method
      // The user needs to revoke access from their Apple ID settings
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }

  // Check if providers are available
  async getAvailableProviders() {
    const providers = [];

    // Google is available on both platforms
    providers.push('google');

    // Apple Sign-In is only available on iOS 13+
    if (Platform.OS === 'ios') {
      const isAppleAvailable = await AppleAuthentication.isAvailableAsync();
      if (isAppleAvailable) {
        providers.push('apple');
      }
    }

    // Facebook is available on both platforms
    providers.push('facebook');

    return providers;
  }
}

export default new SocialAuthService();
