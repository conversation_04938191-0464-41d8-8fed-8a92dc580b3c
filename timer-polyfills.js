// timer-polyfills.js - Add missing timer functions
// This adds polyfills for setImmediate and clearImmediate which are missing

if (typeof global.setImmediate !== 'function') {
    console.log('Adding setImmediate polyfill');
    global.setImmediate = function(callback, ...args) {
      return setTimeout(() => callback(...args), 0);
    };
  }
  
  if (typeof global.clearImmediate !== 'function') {
    console.log('Adding clearImmediate polyfill');
    global.clearImmediate = function(id) {
      return clearTimeout(id);
    };
  }
  
  console.log('Timer polyfills loaded');